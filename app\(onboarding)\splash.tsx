import { Image } from 'expo-image';
import { router } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import React, { useEffect, useRef } from 'react';
import { Animated, Easing, StyleSheet, Text, useColorScheme, View } from 'react-native';
import { COLORS, FONTS, SPACING } from '../../constants/theme';
import { useAuth } from '../../context/AuthContext';

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

export default function Splash() {
  const { token, isLoading } = useAuth();
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const spinAnim = useRef(new Animated.Value(0)).current;
  const loadingDotAnim = useRef(new Animated.Value(0)).current;

  // Create spinning animation
  const spin = spinAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  // Instead of animating text directly, we'll use this value to determine which dots to show
  const dotsIndex = loadingDotAnim.interpolate({
    inputRange: [0, 0.33, 0.66, 1],
    outputRange: [0, 1, 2, 3],
  });

  useEffect(() => {
    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
        easing: Easing.out(Easing.ease),
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
        easing: Easing.out(Easing.back(1.5)),
      }),
      Animated.loop(
        Animated.timing(spinAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
          easing: Easing.linear,
        })
      ),
      Animated.loop(
        Animated.timing(loadingDotAnim, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: false,
          easing: Easing.linear,
        })
      ),
    ]).start();

    // Hide the splash screen after a delay
    const hideSplash = async () => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      await SplashScreen.hideAsync();
    };

    hideSplash();
  }, [fadeAnim, scaleAnim, spinAnim, loadingDotAnim]);

  useEffect(() => {
    // Navigate to the appropriate screen once auth state is loaded
    if (!isLoading) {
      const timer = setTimeout(() => {
        if (token) {
          router.replace('/home');
        } else {
          router.replace('/(onboarding)/welcome');
        }
      }, 3000); // Give a little extra time to show the animations

      return () => clearTimeout(timer);
    }
  }, [isLoading, token]);

  return (
    <View style={[
      styles.container,
      { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
    ]}>
      <Animated.View style={[
        styles.logoContainer,
        {
          opacity: fadeAnim,
          transform: [
            { scale: scaleAnim },
          ],
        }
      ]}>
        <Image
          source={require('../../assets/images/logo.png')}
          style={styles.logo}
          contentFit="contain"
        />
      </Animated.View>

      <Animated.View style={[
        styles.loadingContainer,
        { opacity: fadeAnim }
      ]}>
        <Animated.View style={[
          styles.loadingSpinner,
          {
            borderColor: COLORS.primary,
            transform: [{ rotate: spin }]
          }
        ]} />

        <View style={styles.loadingTextContainer}>
          <Text style={[
            styles.loadingText,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            Loading
          </Text>
          <Text style={[
            styles.loadingText,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            {'.'.repeat(Math.floor((Date.now() / 500) % 4))}
          </Text>
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.xl,
  },
  logo: {
    width: 200,
    height: 200,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingSpinner: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 3,
    borderTopColor: 'transparent',
    marginBottom: SPACING.md,
  },
  loadingTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.medium,
  },
});



