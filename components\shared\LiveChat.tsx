import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
    Animated,
    Dimensions,
    KeyboardAvoidingView,
    Modal,
    Platform,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    useColorScheme,
    View
} from 'react-native';
import { BORDER_RADIUS, COLORS, FONTS, SHADOWS, SPACING } from '../../constants/theme';

interface LiveChatProps {
  isVisible?: boolean;
  onClose?: () => void;
}

const LiveChat: React.FC<LiveChatProps> = ({
  isVisible = false,
  onClose,
}) => {
  const [modalVisible, setModalVisible] = useState(isVisible);
  const [message, setMessage] = useState('');
  const [chatMessages, setChatMessages] = useState<{ text: string; isUser: boolean; timestamp: Date }[]>([
    { text: 'Hello! How can I help you today?', isUser: false, timestamp: new Date() },
  ]);

  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';
  const slideAnim = React.useRef(new Animated.Value(Dimensions.get('window').height)).current;

  React.useEffect(() => {
    setModalVisible(isVisible);
    if (isVisible) {
      // Slide up animation
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      // Slide down animation
      Animated.timing(slideAnim, {
        toValue: Dimensions.get('window').height,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [isVisible, slideAnim]);

  const handleClose = () => {
    // Slide down animation before closing
    Animated.timing(slideAnim, {
      toValue: Dimensions.get('window').height,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setModalVisible(false);
      if (onClose) onClose();
    });
  };

  const handleSend = () => {
    if (message.trim() === '') return;

    // Add user message
    const userMessage = { text: message, isUser: true, timestamp: new Date() };
    setChatMessages(prevMessages => [...prevMessages, userMessage]);
    setMessage('');

    // Simulate agent response after a short delay
    setTimeout(() => {
      const agentMessage = {
        text: 'Thank you for your message. An agent will respond shortly.',
        isUser: false,
        timestamp: new Date()
      };
      setChatMessages(prevMessages => [...prevMessages, agentMessage]);
    }, 1000);
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Floating chat button component
  const FloatingChatButton = () => (
    <TouchableOpacity
      style={[
        styles.floatingButton,
        { backgroundColor: COLORS.primary },
        isDark ? SHADOWS.dark.medium : SHADOWS.light.medium,
      ]}
      onPress={() => {
        console.log('Opening live chat');
        setModalVisible(true);
      }}
    >
      <Ionicons name="chatbubble-ellipses" size={24} color="#FFFFFF" />
    </TouchableOpacity>
  );

  if (!modalVisible && !isVisible) {
    return <FloatingChatButton />;
  }

  return (
    <>
      <Modal
        animationType="none"
        transparent={true}
        visible={modalVisible}
        onRequestClose={handleClose}
      >
        <View style={styles.modalOverlay}>
          <Animated.View
            style={[
              styles.modalContent,
              {
                backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light,
                transform: [{ translateY: slideAnim }]
              },
            ]}
          >
            {/* Chat Header */}
            <View style={[
              styles.chatHeader,
              { backgroundColor: COLORS.primary }
            ]}>
              <View style={styles.headerContent}>
                <Text style={styles.headerTitle}>Live Support</Text>
                <View style={styles.agentStatus}>
                  <View style={styles.statusDot} />
                  <Text style={styles.statusText}>Agent Online</Text>
                </View>
              </View>
              <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
                <Ionicons name="close" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            </View>

            {/* Chat Messages */}
            <View style={styles.messagesContainer}>
              {chatMessages.map((msg, index) => (
                <View
                  key={index}
                  style={[
                    styles.messageBubble,
                    msg.isUser ? styles.userMessage : styles.agentMessage,
                    {
                      backgroundColor: msg.isUser
                        ? COLORS.primary
                        : isDark ? COLORS.ui.dark.card : COLORS.ui.light.card
                    }
                  ]}
                >
                  <Text style={[
                    styles.messageText,
                    {
                      color: msg.isUser
                        ? '#FFFFFF'
                        : isDark ? COLORS.text.dark.primary : COLORS.text.light.primary
                    }
                  ]}>
                    {msg.text}
                  </Text>
                  <Text style={styles.timestamp}>
                    {formatTime(msg.timestamp)}
                  </Text>
                </View>
              ))}
            </View>

            {/* Message Input */}
            <KeyboardAvoidingView
              behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
              style={styles.inputContainer}
            >
              <TextInput
                style={[
                  styles.input,
                  {
                    backgroundColor: isDark ? COLORS.ui.dark.input : COLORS.ui.light.input,
                    color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary,
                  }
                ]}
                placeholder="Type a message..."
                placeholderTextColor={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
                value={message}
                onChangeText={setMessage}
                multiline
              />
              <TouchableOpacity
                style={[styles.sendButton, { backgroundColor: COLORS.primary }]}
                onPress={handleSend}
              >
                <Ionicons name="send" size={20} color="#FFFFFF" />
              </TouchableOpacity>
            </KeyboardAvoidingView>
          </Animated.View>
        </View>
      </Modal>

      {!isVisible && <FloatingChatButton />}
    </>
  );
};

const styles = StyleSheet.create({
  floatingButton: {
    position: 'absolute',
    bottom: SPACING.xl,
    right: SPACING.lg,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    height: '80%',
    borderTopLeftRadius: BORDER_RADIUS.lg,
    borderTopRightRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
  },
  chatHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: SPACING.md,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    color: '#FFFFFF',
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.bold,
  },
  agentStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: SPACING.xs,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: COLORS.status.success,
    marginRight: SPACING.xs,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: FONTS.sizes.xs,
    fontFamily: FONTS.regular,
  },
  closeButton: {
    padding: SPACING.xs,
  },
  messagesContainer: {
    flex: 1,
    padding: SPACING.md,
  },
  messageBubble: {
    maxWidth: '80%',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.md,
  },
  userMessage: {
    alignSelf: 'flex-end',
  },
  agentMessage: {
    alignSelf: 'flex-start',
  },
  messageText: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
  },
  timestamp: {
    fontSize: FONTS.sizes.xs,
    color: 'rgba(255, 255, 255, 0.7)',
    alignSelf: 'flex-end',
    marginTop: SPACING.xs,
  },
  inputContainer: {
    flexDirection: 'row',
    padding: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  input: {
    flex: 1,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.sm,
    maxHeight: 100,
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: SPACING.sm,
  },
});

export default LiveChat;
