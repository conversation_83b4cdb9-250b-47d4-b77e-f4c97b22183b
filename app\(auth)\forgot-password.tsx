import { Ionicons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import { Link, router } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  useColorScheme,
  View,
} from 'react-native';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import { COLORS, FONTS, SPACING } from '../../constants/theme';
import { useAuth } from '../../context/AuthContext';
import { useFormValidation } from '../../hooks/useFormValidation';

export default function ForgotPassword() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState(1); // 1: Email entry, 2: OTP verification, 3: New password
  const [otp, setOtp] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errors, setErrors] = useState<{
    email?: string;
    otp?: string;
    newPassword?: string;
    confirmPassword?: string;
  }>({});

  const { forgotPassword, resetPassword } = useAuth();
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';
  const { validators } = useFormValidation({});

  const validateEmail = () => {
    const emailError = validators.validateEmail(email);
    setErrors({ email: emailError });
    return !emailError;
  };

  const validateOtp = () => {
    const newErrors: { otp?: string } = {};

    if (!otp) {
      newErrors.otp = 'OTP is required';
    } else if (otp.length !== 6) {
      newErrors.otp = 'OTP must be 6 digits';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateNewPassword = () => {
    const newErrors: { newPassword?: string; confirmPassword?: string } = {};

    if (!newPassword) {
      newErrors.newPassword = 'New password is required';
    } else if (newPassword.length < 6) {
      newErrors.newPassword = 'Password must be at least 6 characters';
    }

    if (!confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (confirmPassword !== newPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRequestReset = async () => {
    if (!validateEmail()) return;

    setIsLoading(true);
    try {
      // Log the reset request
      console.log('Password reset requested for:', email);

      await forgotPassword(email);
      setStep(2);
      Alert.alert(
        'OTP Sent',
        'A verification code has been sent to your email.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert(
        'Request Failed',
        'There was an error sending the reset code. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOtp = () => {
    if (!validateOtp()) return;

    // Log the OTP verification
    console.log('OTP verification attempt:', { email, otp });

    // In a real app, you would verify the OTP with the backend
    // For this demo, we'll just move to the next step
    setStep(3);
  };

  const handleResetPassword = async () => {
    if (!validateNewPassword()) return;

    setIsLoading(true);
    try {
      // Log the password reset
      console.log('Password reset:', { email, newPassword });

      await resetPassword(email, newPassword);
      Alert.alert(
        'Password Reset',
        'Your password has been reset successfully. Please login with your new password.',
        [{
          text: 'Login',
          onPress: () => router.replace('/(auth)/login')
        }]
      );
    } catch (error) {
      Alert.alert(
        'Reset Failed',
        'There was an error resetting your password. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const renderStep = () => {
    switch (step) {
      case 1:
        return (
          <>
            <Text style={[
              styles.title,
              { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
            ]}>
              Forgot Password
            </Text>

            <Text style={[
              styles.subtitle,
              { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
            ]}>
              Enter your email to reset your password
            </Text>

            <View style={styles.formContainer}>
              <Input
                label="Email"
                placeholder="Enter your email"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                error={errors.email}
                leftIcon={<Ionicons name="mail-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
              />

              <Button
                title="Send Reset Code"
                onPress={handleRequestReset}
                isLoading={isLoading}
                style={styles.actionButton}
                size="large"
              />
            </View>
          </>
        );

      case 2:
        return (
          <>
            <Text style={[
              styles.title,
              { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
            ]}>
              Verify OTP
            </Text>

            <Text style={[
              styles.subtitle,
              { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
            ]}>
              Enter the verification code sent to your email
            </Text>

            <View style={styles.formContainer}>
              <Input
                label="Verification Code"
                placeholder="Enter 6-digit code"
                value={otp}
                onChangeText={setOtp}
                keyboardType="number-pad"
                maxLength={6}
                error={errors.otp}
                leftIcon={<Ionicons name="key-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
              />

              <Button
                title="Verify Code"
                onPress={handleVerifyOtp}
                style={styles.actionButton}
                size="large"
              />

              <TouchableOpacity
                style={styles.resendContainer}
                onPress={handleRequestReset}
              >
                <Text style={[
                  styles.resendText,
                  { color: COLORS.primary }
                ]}>
                  Resend Code
                </Text>
              </TouchableOpacity>
            </View>
          </>
        );

      case 3:
        return (
          <>
            <Text style={[
              styles.title,
              { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
            ]}>
              Reset Password
            </Text>

            <Text style={[
              styles.subtitle,
              { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
            ]}>
              Create a new password for your account
            </Text>

            <View style={styles.formContainer}>
              <Input
                label="New Password"
                placeholder="Enter new password"
                value={newPassword}
                onChangeText={setNewPassword}
                isPassword
                error={errors.newPassword}
                leftIcon={<Ionicons name="lock-closed-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
              />

              <Input
                label="Confirm Password"
                placeholder="Confirm new password"
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                isPassword
                error={errors.confirmPassword}
                leftIcon={<Ionicons name="lock-closed-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
              />

              <Button
                title="Reset Password"
                onPress={handleResetPassword}
                isLoading={isLoading}
                style={styles.actionButton}
                size="large"
              />
            </View>
          </>
        );

      default:
        return null;
    }
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={[
          styles.container,
          { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
        ]}
      >
        <View style={styles.logoContainer}>
          <Image
            source={require('../../assets/images/logo.png')}
            style={styles.logo}
            contentFit="contain"
          />
        </View>

        {renderStep()}

        <View style={styles.loginContainer}>
          <Text style={[
            styles.loginText,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            Remember your password?
          </Text>
          <Link href="/login" asChild>
            <TouchableOpacity>
              <Text style={[
                styles.loginLink,
                { color: COLORS.primary }
              ]}>
                Login
              </Text>
            </TouchableOpacity>
          </Link>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  logoContainer: {
    marginBottom: SPACING.xl,
  },
  logo: {
    width: 100,
    height: 100,
  },
  title: {
    fontSize: FONTS.sizes.xxl,
    fontFamily: FONTS.bold,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    marginBottom: SPACING.xl,
    textAlign: 'center',
  },
  formContainer: {
    width: '100%',
    maxWidth: 400,
  },
  actionButton: {
    marginTop: SPACING.md,
    marginBottom: SPACING.xl,
  },
  resendContainer: {
    alignItems: 'center',
    marginTop: -SPACING.lg,
    marginBottom: SPACING.xl,
  },
  resendText: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.medium,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: SPACING.md,
  },
  loginText: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    marginRight: SPACING.xs,
  },
  loginLink: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.bold,
  },
});

