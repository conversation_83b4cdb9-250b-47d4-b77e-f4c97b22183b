import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Platform,
  useColorScheme,
} from 'react-native';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../../constants/theme';

interface DatePickerProps {
  label: string;
  value: string;
  onChange: (date: string) => void;
  error?: string;
  placeholder?: string;
}

const DatePicker: React.FC<DatePickerProps> = ({
  label,
  value,
  onChange,
  error,
  placeholder = 'Select date',
}) => {
  const [date, setDate] = useState<Date>(value ? new Date(value) : new Date());
  const [showPicker, setShowPicker] = useState(false);
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  const handleChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setShowPicker(false);
    }
    
    if (selectedDate) {
      setDate(selectedDate);
      const formattedDate = selectedDate.toISOString().split('T')[0]; // YYYY-MM-DD format
      onChange(formattedDate);
    }
  };

  const showDatepicker = () => {
    setShowPicker(true);
  };

  const formatDisplayDate = (dateString: string) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <View style={styles.container}>
      {label && (
        <Text
          style={[
            styles.label,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary },
          ]}
        >
          {label}
        </Text>
      )}
      
      <TouchableOpacity
        onPress={showDatepicker}
        style={[
          styles.inputContainer,
          {
            backgroundColor: isDark ? COLORS.ui.dark.input : COLORS.ui.light.input,
            borderColor: error 
              ? COLORS.status.error 
              : isDark ? COLORS.ui.dark.border : COLORS.ui.light.border,
          },
        ]}
      >
        <Ionicons 
          name="calendar-outline" 
          size={20} 
          color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} 
          style={styles.icon}
        />
        
        <Text
          style={[
            styles.dateText,
            {
              color: value 
                ? (isDark ? COLORS.text.dark.primary : COLORS.text.light.primary)
                : (isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary)
            },
          ]}
        >
          {value ? formatDisplayDate(value) : placeholder}
        </Text>
      </TouchableOpacity>
      
      {error && (
        <Text style={styles.error}>
          {error}
        </Text>
      )}

      {showPicker && (
        Platform.OS === 'ios' ? (
          <Modal
            animationType="slide"
            transparent={true}
            visible={showPicker}
            onRequestClose={() => setShowPicker(false)}
          >
            <View style={styles.modalContainer}>
              <View
                style={[
                  styles.modalContent,
                  { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
                ]}
              >
                <View style={styles.modalHeader}>
                  <TouchableOpacity onPress={() => setShowPicker(false)}>
                    <Text style={{ color: COLORS.primary }}>Cancel</Text>
                  </TouchableOpacity>
                  
                  <Text style={[
                    styles.modalTitle,
                    { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
                  ]}>
                    Select Date
                  </Text>
                  
                  <TouchableOpacity onPress={() => {
                    setShowPicker(false);
                    const formattedDate = date.toISOString().split('T')[0];
                    onChange(formattedDate);
                  }}>
                    <Text style={{ color: COLORS.primary }}>Done</Text>
                  </TouchableOpacity>
                </View>
                
                <DateTimePicker
                  value={date}
                  mode="date"
                  display="spinner"
                  onChange={handleChange}
                  style={styles.datePicker}
                  textColor={isDark ? COLORS.text.dark.primary : COLORS.text.light.primary}
                />
              </View>
            </View>
          </Modal>
        ) : (
          <DateTimePicker
            value={date}
            mode="date"
            display="default"
            onChange={handleChange}
          />
        )
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.md,
  },
  label: {
    fontSize: FONTS.sizes.sm,
    marginBottom: SPACING.xs,
    fontFamily: FONTS.medium,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: BORDER_RADIUS.md,
    overflow: 'hidden',
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
  },
  icon: {
    marginRight: SPACING.sm,
  },
  dateText: {
    flex: 1,
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
  },
  error: {
    color: COLORS.status.error,
    fontSize: FONTS.sizes.sm,
    marginTop: SPACING.xs,
    fontFamily: FONTS.regular,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: BORDER_RADIUS.lg,
    borderTopRightRadius: BORDER_RADIUS.lg,
    paddingBottom: SPACING.xl,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalTitle: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.bold,
  },
  datePicker: {
    height: 200,
  },
});

export default DatePicker;
