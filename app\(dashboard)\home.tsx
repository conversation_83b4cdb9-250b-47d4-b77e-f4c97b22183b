import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  useColorScheme,
  View
} from 'react-native';
import Carousel, { CarouselItem } from '../../components/ui/Carousel';
import Header from '../../components/ui/Header';
import ProfileCompletionBanner from '../../components/ui/ProfileCompletionBanner';
import Sidebar from '../../components/ui/Sidebar';
import { BORDER_RADIUS, COLORS, FONTS, SHADOWS, SPACING } from '../../constants/theme';

export default function Home() {
  const [isSidebarVisible, setIsSidebarVisible] = useState(false);
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  const handleNotificationPress = () => {
    console.log('Notification pressed');
    router.push('/(dashboard)/notifications');
  };

  const handleMenuPress = () => {
    console.log('Menu pressed');
    setIsSidebarVisible(true);
  };

  const handleCloseSidebar = () => {
    setIsSidebarVisible(false);
  };

  // Quick actions data
  const quickActions = [
    { icon: 'calculator-outline', title: 'Get Quote', color: COLORS.primary, route: '/(dashboard)/quote' as const },
    { icon: 'shield-checkmark-outline', title: 'My Policies', color: COLORS.secondary, route: '/(dashboard)/policies' as const },
    { icon: 'clipboard-outline', title: 'Claims', color: COLORS.status.warning, route: '/(dashboard)/claims' as const },
    { icon: 'folder-outline', title: 'Documents', color: COLORS.status.info, route: '/(dashboard)/documents' as const },
  ];

  // Promotional banners data
  const promotionalBanners: CarouselItem[] = [
    {
      id: '1',
      title: 'Special Offer!',
      description: 'Get 15% off on new policies this month',
      backgroundColor: COLORS.primary,
      icon: 'arrow-forward-circle',
      image: require('../../assets/images/logo.png'),
      actionText: 'Learn More',
    },
    {
      id: '2',
      title: 'Bundle & Save',
      description: 'Save up to 20% when you bundle home & auto',
      backgroundColor: COLORS.secondary,
      icon: 'arrow-forward-circle',
      image: require('../../assets/images/logo.png'),
      actionText: 'Learn More',
    },
    {
      id: '3',
      title: 'New Mobile App',
      description: 'Manage your policies on the go with our new app',
      backgroundColor: '#4A90E2',
      icon: 'arrow-forward-circle',
      image: require('../../assets/images/logo.png'),
      actionText: 'Learn More',
    },
  ];

  // Policy cards data
  const policyCards: CarouselItem[] = [
    {
      id: '1',
      title: 'Auto Insurance',
      policyNumber: 'AUT-2023-45678',
      coverageType: 'Comprehensive',
      expiryDate: 'Dec 31, 2023',
      icon: 'car-outline',
      backgroundColor: COLORS.primary,
      actionText: 'View Details',
    },
    {
      id: '2',
      title: 'Home Insurance',
      policyNumber: 'HOM-2023-98765',
      coverageType: 'Standard',
      expiryDate: 'Mar 15, 2024',
      icon: 'home-outline',
      backgroundColor: COLORS.secondary,
      actionText: 'View Details',
    },
    {
      id: '3',
      title: 'Get a New Quote',
      icon: 'add-circle-outline',
      backgroundColor: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary,
      description: 'Explore our insurance options',
      actionText: 'Get Quote',
    },
  ];

  // Renewal reminders data
  const renewalReminders: CarouselItem[] = [
    {
      id: '1',
      title: 'Auto Insurance Renewal',
      description: 'Your policy expires in 30 days',
      backgroundColor: COLORS.status.warning,
      actionText: 'Renew Now',
    },
    {
      id: '2',
      title: 'Payment Due',
      description: 'Home insurance payment due in 7 days',
      backgroundColor: COLORS.status.info,
      actionText: 'Pay Now',
    },
    {
      id: '3',
      title: 'Policy Review',
      description: 'Schedule your annual policy review',
      backgroundColor: COLORS.secondary,
      actionText: 'Schedule',
    },
  ];

  const handleBannerPress = (item: CarouselItem) => {
    console.log('Banner pressed:', item.title);
  };

  const handlePolicyPress = (item: CarouselItem) => {
    if (item.id === '3') {
      router.push('/(dashboard)/quote');
    } else {
      router.push('/(dashboard)/policies');
    }
  };

  const handleReminderPress = (item: CarouselItem) => {
    console.log('Reminder pressed:', item.title);
    // In a real app, you would handle different actions based on the reminder type
    if (item.title.includes('Renewal')) {
      router.push('/(dashboard)/policies');
    } else if (item.title.includes('Payment')) {
      // Handle payment action
      console.log('Handle payment');
    } else {
      // Handle other reminder actions
      console.log('Handle other reminder action');
    }
  };

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
    ]}>
      <Sidebar
        isVisible={isSidebarVisible}
        onClose={handleCloseSidebar}
      />
      <Header
        showBackButton={false}
        showGreeting={true}
        showNotification={true}
        showMenu={true}
        onNotificationPress={handleNotificationPress}
        onMenuPress={handleMenuPress}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Profile Completion Banner */}
        <ProfileCompletionBanner />

        {/* Promotional Banners - Main Carousel */}
        <Text style={[
          styles.sectionTitle,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          Special Offers
        </Text>

        <View style={styles.carouselContainer}>
          <Carousel
            data={promotionalBanners}
            type="banner"
            onItemPress={handleBannerPress}
            autoPlay={true}
            autoPlayInterval={8000}
            showPagination={true}
            direction="forward"
          />
        </View>

        {/* Quick Action Buttons */}
        <Text style={[
          styles.sectionTitle,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          Quick Actions
        </Text>

        <View style={styles.quickActionsContainer}>
          {quickActions.map((action) => (
            <TouchableOpacity
              key={action.title}
              style={[
                styles.quickActionButton,
                { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
              ]}
              onPress={() => router.push(action.route)}
            >
              <Ionicons name={action.icon as any} size={28} color={action.color} />
              <Text style={[
                styles.quickActionText,
                { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
              ]}>
                {action.title}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Current Policy Summary Cards */}
        <Text style={[
          styles.sectionTitle,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          Current Policies
        </Text>

        <View style={styles.carouselContainer}>
          <Carousel
            data={policyCards}
            type="policy"
            onItemPress={handlePolicyPress}
            autoPlay={true}
            autoPlayInterval={10000}
            showPagination={true}
            direction="backward"
          />
        </View>

        {/* Renewal Reminders */}
        <Text style={[
          styles.sectionTitle,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          Renewal Reminders
        </Text>

        <View style={styles.carouselContainer}>
          <Carousel
            data={renewalReminders}
            type="banner"
            onItemPress={handleReminderPress}
            autoPlay={true}
            autoPlayInterval={9000}
            showPagination={true}
            direction="forward"
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  // Layout
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: SPACING.lg,
    paddingBottom: SPACING.xxl,
  },
  horizontalScrollView: {
    marginBottom: SPACING.md,
  },
  carouselContainer: {
    marginBottom: SPACING.md,
  },

  // Section Titles
  sectionTitle: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.bold,
    marginTop: SPACING.lg,
    marginBottom: SPACING.md,
  },

  // Cards
  card: {
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.lg,
    marginBottom: SPACING.md,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  cardTitle: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.bold,
  },

  // Policy Cards
  policyCard: {
    width: 280,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    marginRight: SPACING.md,
    ...SHADOWS.light.small,
  },
  policyCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  policyCardType: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.bold,
    marginLeft: SPACING.sm,
  },
  policyCardNumber: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.regular,
    marginBottom: SPACING.md,
  },
  policyCardDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.xs,
  },
  policyCardLabel: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.medium,
  },
  policyCardValue: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.bold,
  },
  policyCardButton: {
    backgroundColor: COLORS.primary,
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.sm,
    alignItems: 'center',
    marginTop: SPACING.md,
  },
  policyCardButtonText: {
    color: '#FFFFFF',
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.medium,
  },
  newPolicyCard: {
    width: 150,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    marginRight: SPACING.md,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: 'rgba(0,0,0,0.1)',
  },
  newPolicyText: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.medium,
    marginTop: SPACING.sm,
    textAlign: 'center',
  },

  // Reminder Cards
  reminderCard: {
    width: 280,
    borderRadius: BORDER_RADIUS.md,
    marginRight: SPACING.md,
    flexDirection: 'row',
    overflow: 'hidden',
    ...SHADOWS.light.small,
  },
  reminderIndicator: {
    width: 8,
  },
  reminderContent: {
    flex: 1,
    padding: SPACING.md,
  },
  reminderTitle: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.bold,
    marginBottom: SPACING.xs,
  },
  reminderText: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.regular,
    marginBottom: SPACING.md,
  },
  reminderButton: {
    backgroundColor: COLORS.primary,
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.sm,
    alignItems: 'center',
  },
  reminderButtonText: {
    color: '#FFFFFF',
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.medium,
  },

  // Promotional Banners
  promotionalBanner: {
    width: 280,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.lg,
    borderRadius: BORDER_RADIUS.md,
    marginRight: SPACING.md,
  },
  promotionalTitle: {
    color: '#FFFFFF',
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.bold,
    marginBottom: SPACING.xs,
  },
  promotionalText: {
    color: '#FFFFFF',
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    maxWidth: 180,
  },

  // Quick Actions
  quickActionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: SPACING.lg,
  },
  quickActionButton: {
    width: '48%',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.md,
    ...SHADOWS.light.small,
  },
  quickActionText: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.medium,
    marginTop: SPACING.sm,
  },

  // Notifications
  notificationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  notificationDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: SPACING.sm,
  },
  notificationText: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    flex: 1,
  },
});
