import React, { useState } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  TextInputProps,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
  useColorScheme,
} from 'react-native';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../../constants/theme';
import { Ionicons } from '@expo/vector-icons';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  inputStyle?: TextStyle;
  errorStyle?: TextStyle;
  isPassword?: boolean;
  leftIcon?: React.ReactNode;
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  containerStyle,
  labelStyle,
  inputStyle,
  errorStyle,
  isPassword = false,
  leftIcon,
  ...props
}) => {
  const [secureTextEntry, setSecureTextEntry] = useState(isPassword);
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  const toggleSecureEntry = () => {
    setSecureTextEntry(!secureTextEntry);
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <Text
          style={[
            styles.label,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary },
            labelStyle,
          ]}
        >
          {label}
        </Text>
      )}
      <View style={[
        styles.inputContainer,
        {
          backgroundColor: isDark ? COLORS.ui.dark.input : COLORS.ui.light.input,
          borderColor: error 
            ? COLORS.status.error 
            : isDark ? COLORS.ui.dark.border : COLORS.ui.light.border,
        },
      ]}>
        {leftIcon && <View style={styles.leftIconContainer}>{leftIcon}</View>}
        <TextInput
          style={[
            styles.input,
            {
              color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary,
            },
            inputStyle,
          ]}
          placeholderTextColor={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
          secureTextEntry={secureTextEntry}
          {...props}
        />
        {isPassword && (
          <TouchableOpacity onPress={toggleSecureEntry} style={styles.rightIconContainer}>
            <Ionicons
              name={secureTextEntry ? 'eye-off' : 'eye'}
              size={24}
              color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
            />
          </TouchableOpacity>
        )}
      </View>
      {error && (
        <Text style={[styles.error, errorStyle]}>
          {error}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.md,
  },
  label: {
    fontSize: FONTS.sizes.sm,
    marginBottom: SPACING.xs,
    fontFamily: FONTS.medium,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: BORDER_RADIUS.md,
    overflow: 'hidden',
  },
  input: {
    flex: 1,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
  },
  leftIconContainer: {
    paddingLeft: SPACING.md,
  },
  rightIconContainer: {
    padding: SPACING.sm,
  },
  error: {
    color: COLORS.status.error,
    fontSize: FONTS.sizes.sm,
    marginTop: SPACING.xs,
    fontFamily: FONTS.regular,
  },
});

export default Input;
