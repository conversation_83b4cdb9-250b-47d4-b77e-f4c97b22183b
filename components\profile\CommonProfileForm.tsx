import React from 'react';
import { Ionicons } from '@expo/vector-icons';
import { useColorScheme } from 'react-native';
import Input from '../ui/Input';
import { COLORS } from '../../constants/theme';
import { ProfileFormData, ProfileFormErrors } from '../../hooks/useProfileForm';

interface CommonProfileFormProps {
  formData: ProfileFormData;
  errors: ProfileFormErrors;
  updateField: (field: keyof ProfileFormData, value: string) => void;
  isEditing: boolean;
}

const CommonProfileForm: React.FC<CommonProfileFormProps> = ({
  formData,
  errors,
  updateField,
  isEditing,
}) => {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  return (
    <>
      <Input
        label="Full Name"
        placeholder="Enter your full name"
        value={formData.fullName}
        onChangeText={(value) => updateField('fullName', value)}
        error={errors.fullName}
        editable={isEditing}
        leftIcon={<Ionicons name="person-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
      />

      <Input
        label="Email"
        placeholder="Enter your email"
        value={formData.email}
        onChangeText={(value) => updateField('email', value)}
        keyboardType="email-address"
        autoCapitalize="none"
        error={errors.email}
        editable={isEditing}
        leftIcon={<Ionicons name="mail-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
      />

      <Input
        label="Phone Number"
        placeholder="Enter your phone number"
        value={formData.phoneNumber}
        onChangeText={(value) => updateField('phoneNumber', value)}
        keyboardType="phone-pad"
        error={errors.phoneNumber}
        editable={isEditing}
        leftIcon={<Ionicons name="call-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
      />
    </>
  );
};

export default CommonProfileForm;
