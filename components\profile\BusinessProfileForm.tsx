import React from 'react';
import { Ionicons } from '@expo/vector-icons';
import { useColorScheme } from 'react-native';
import Input from '../ui/Input';
import { COLORS } from '../../constants/theme';
import { ProfileFormData, ProfileFormErrors } from '../../hooks/useProfileForm';

interface BusinessProfileFormProps {
  formData: ProfileFormData;
  errors: ProfileFormErrors;
  updateField: (field: keyof ProfileFormData, value: string) => void;
  isEditing: boolean;
}

const BusinessProfileForm: React.FC<BusinessProfileFormProps> = ({
  formData,
  errors,
  updateField,
  isEditing,
}) => {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  return (
    <>
      <Input
        label="Business Name"
        placeholder="Enter business name"
        value={formData.businessName}
        onChangeText={(value) => updateField('businessName', value)}
        error={errors.businessName}
        editable={isEditing}
        leftIcon={<Ionicons name="business-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
      />

      <Input
        label="Business Location"
        placeholder="Enter business location"
        value={formData.businessLocation}
        onChangeText={(value) => updateField('businessLocation', value)}
        error={errors.businessLocation}
        editable={isEditing}
        leftIcon={<Ionicons name="location-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
      />

      <Input
        label="Business Address"
        placeholder="Enter business address"
        value={formData.address}
        onChangeText={(value) => updateField('address', value)}
        error={errors.address}
        editable={isEditing}
        leftIcon={<Ionicons name="home-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
      />
    </>
  );
};

export default BusinessProfileForm;
