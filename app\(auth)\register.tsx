import { Ionicons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import { Link } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  useColorScheme,
  View,
} from 'react-native';
import Button from '../../components/ui/Button';
import DatePicker from '../../components/ui/DatePicker';
import Input from '../../components/ui/Input';
import { BORDER_RADIUS, COLORS, FONTS, SPACING } from '../../constants/theme';
import { BusinessUserData, IndividualUserData, useAuth } from '../../context/AuthContext';

export default function Register() {
  const [userType, setUserType] = useState<'business' | 'individual' | null>(null);
  const [step, setStep] = useState(1); // 1: User type selection, 2: Registration form
  const [isLoading, setIsLoading] = useState(false);

  // Common fields
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [acceptTerms, setAcceptTerms] = useState(false);

  // Business fields
  const [businessName, setBusinessName] = useState('');
  const [businessLocation, setBusinessLocation] = useState('');
  const [businessAddress, setBusinessAddress] = useState('');
  const [contactPerson, setContactPerson] = useState('');

  // Individual fields
  const [fullName, setFullName] = useState('');
  const [dateOfBirth, setDateOfBirth] = useState('');
  const [physicalAddress, setPhysicalAddress] = useState('');
  const [occupation, setOccupation] = useState('');

  const [errors, setErrors] = useState<{
    email?: string;
    password?: string;
    confirmPassword?: string;
    phoneNumber?: string;
    businessName?: string;
    businessLocation?: string;
    businessAddress?: string;
    contactPerson?: string;
    fullName?: string;
    dateOfBirth?: string;
    physicalAddress?: string;
    occupation?: string;
    terms?: string;
  }>({});

  const { signUp } = useAuth();
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  const selectUserType = (type: 'business' | 'individual') => {
    setUserType(type);
    setStep(2);
  };

  const validateCommonFields = () => {
    const newErrors: any = {};

    // Email validation
    if (!email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email is invalid';
    }

    // Password validation
    if (!password) {
      newErrors.password = 'Password is required';
    } else if (password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    // Confirm password validation
    if (!confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (confirmPassword !== password) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    // Phone number validation
    if (!phoneNumber) {
      newErrors.phoneNumber = 'Phone number is required';
    }

    // Terms validation
    if (!acceptTerms) {
      newErrors.terms = 'You must accept the terms and conditions';
    }

    return newErrors;
  };

  const validateBusinessFields = () => {
    const newErrors = validateCommonFields();

    if (!businessName) {
      newErrors.businessName = 'Business name is required';
    }

    if (!businessLocation) {
      newErrors.businessLocation = 'Business location is required';
    }

    if (!businessAddress) {
      newErrors.businessAddress = 'Business address is required';
    }

    if (!contactPerson) {
      newErrors.contactPerson = 'Contact person name is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateIndividualFields = () => {
    const newErrors = validateCommonFields();

    if (!fullName) {
      newErrors.fullName = 'Full name is required';
    }

    if (!dateOfBirth) {
      newErrors.dateOfBirth = 'Date of birth is required';
    }

    if (!physicalAddress) {
      newErrors.physicalAddress = 'Physical address is required';
    }

    if (!occupation) {
      newErrors.occupation = 'Occupation is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRegister = async () => {
    if (!userType) return;

    const isValid = userType === 'business'
      ? validateBusinessFields()
      : validateIndividualFields();

    if (!isValid) return;

    setIsLoading(true);
    try {
      if (userType === 'business') {
        const businessData: BusinessUserData = {
          user_type: 'business',
          email,
          password,
          phone_number: phoneNumber,
          business_name: businessName,
          business_location: businessLocation,
          business_address: businessAddress,
          contact_person: contactPerson,
        };
        await signUp(businessData);
      } else {
        const individualData: IndividualUserData = {
          user_type: 'individual',
          email,
          password,
          phone_number: phoneNumber,
          full_name: fullName,
          date_of_birth: dateOfBirth,
          physical_address: physicalAddress,
          occupation,
        };
        await signUp(individualData);
      }
      // Navigation is handled in the signUp function
    } catch (error) {
      Alert.alert(
        'Registration Failed',
        'There was an error during registration. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const renderUserTypeSelection = () => (
    <>
      <Text style={[
        styles.title,
        { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
      ]}>
        Create Account
      </Text>

      <Text style={[
        styles.subtitle,
        { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
      ]}>
        Select account type
      </Text>

      <View style={styles.userTypeContainer}>
        <TouchableOpacity
          style={[
            styles.userTypeCard,
            { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
          ]}
          onPress={() => selectUserType('business')}
        >
          <Ionicons
            name="business-outline"
            size={48}
            color={COLORS.primary}
            style={styles.userTypeIcon}
          />
          <Text style={[
            styles.userTypeTitle,
            { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
          ]}>
            Business
          </Text>
          <Text style={[
            styles.userTypeDescription,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            Register as a business entity
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.userTypeCard,
            { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
          ]}
          onPress={() => selectUserType('individual')}
        >
          <Ionicons
            name="person-outline"
            size={48}
            color={COLORS.secondary}
            style={styles.userTypeIcon}
          />
          <Text style={[
            styles.userTypeTitle,
            { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
          ]}>
            Individual
          </Text>
          <Text style={[
            styles.userTypeDescription,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            Register as an individual
          </Text>
        </TouchableOpacity>
      </View>
    </>
  );

  const renderBusinessForm = () => (
    <>
      <Text style={[
        styles.title,
        { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
      ]}>
        Business Registration
      </Text>

      <Text style={[
        styles.subtitle,
        { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
      ]}>
        Enter your business details
      </Text>

      <View style={styles.formContainer}>
        <Input
          label="Business Name"
          placeholder="Enter business name"
          value={businessName}
          onChangeText={setBusinessName}
          error={errors.businessName}
          leftIcon={<Ionicons name="business-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
        />

        <Input
          label="Business Location"
          placeholder="Enter business location"
          value={businessLocation}
          onChangeText={setBusinessLocation}
          error={errors.businessLocation}
          leftIcon={<Ionicons name="location-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
        />

        <Input
          label="Business Address"
          placeholder="Enter business address"
          value={businessAddress}
          onChangeText={setBusinessAddress}
          error={errors.businessAddress}
          leftIcon={<Ionicons name="map-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
        />

        <Input
          label="Contact Person"
          placeholder="Enter contact person name"
          value={contactPerson}
          onChangeText={setContactPerson}
          error={errors.contactPerson}
          leftIcon={<Ionicons name="person-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
        />

        <Input
          label="Email"
          placeholder="Enter business email"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
          error={errors.email}
          leftIcon={<Ionicons name="mail-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
        />

        <Input
          label="Phone Number"
          placeholder="Enter business phone number"
          value={phoneNumber}
          onChangeText={setPhoneNumber}
          keyboardType="phone-pad"
          error={errors.phoneNumber}
          leftIcon={<Ionicons name="call-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
        />

        <Input
          label="Password"
          placeholder="Create a password"
          value={password}
          onChangeText={setPassword}
          isPassword
          error={errors.password}
          leftIcon={<Ionicons name="lock-closed-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
        />

        <Input
          label="Confirm Password"
          placeholder="Confirm your password"
          value={confirmPassword}
          onChangeText={setConfirmPassword}
          isPassword
          error={errors.confirmPassword}
          leftIcon={<Ionicons name="lock-closed-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
        />

        <TouchableOpacity
          style={styles.termsContainer}
          onPress={() => setAcceptTerms(!acceptTerms)}
        >
          <View style={[
            styles.checkbox,
            {
              backgroundColor: acceptTerms ? COLORS.primary : 'transparent',
              borderColor: acceptTerms ? COLORS.primary : isDark ? COLORS.ui.dark.border : COLORS.ui.light.border,
            }
          ]}>
            {acceptTerms && (
              <Ionicons name="checkmark" size={16} color="#FFFFFF" />
            )}
          </View>
          <Text style={[
            styles.termsText,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            I accept the Terms and Conditions
          </Text>
        </TouchableOpacity>

        {errors.terms && (
          <Text style={styles.termsError}>{errors.terms}</Text>
        )}

        <Button
          title="Register"
          onPress={handleRegister}
          isLoading={isLoading}
          style={styles.registerButton}
          size="large"
        />

        <Button
          title="Back"
          onPress={() => setStep(1)}
          variant="outline"
          style={styles.backButton}
        />
      </View>
    </>
  );

  const renderIndividualForm = () => (
    <>
      <Text style={[
        styles.title,
        { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
      ]}>
        Individual Registration
      </Text>

      <Text style={[
        styles.subtitle,
        { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
      ]}>
        Enter your personal details
      </Text>

      <View style={styles.formContainer}>
        <Input
          label="Full Name"
          placeholder="Enter your full name"
          value={fullName}
          onChangeText={setFullName}
          error={errors.fullName}
          leftIcon={<Ionicons name="person-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
        />

        <DatePicker
          label="Date of Birth"
          placeholder="Select date of birth"
          value={dateOfBirth}
          onChange={setDateOfBirth}
          error={errors.dateOfBirth}
        />

        <Input
          label="Physical Address"
          placeholder="Enter your address"
          value={physicalAddress}
          onChangeText={setPhysicalAddress}
          error={errors.physicalAddress}
          leftIcon={<Ionicons name="home-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
        />

        <Input
          label="Occupation"
          placeholder="Enter your occupation"
          value={occupation}
          onChangeText={setOccupation}
          error={errors.occupation}
          leftIcon={<Ionicons name="briefcase-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
        />

        <Input
          label="Email"
          placeholder="Enter your email"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
          error={errors.email}
          leftIcon={<Ionicons name="mail-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
        />

        <Input
          label="Phone Number"
          placeholder="Enter your phone number"
          value={phoneNumber}
          onChangeText={setPhoneNumber}
          keyboardType="phone-pad"
          error={errors.phoneNumber}
          leftIcon={<Ionicons name="call-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
        />

        <Input
          label="Password"
          placeholder="Create a password"
          value={password}
          onChangeText={setPassword}
          isPassword
          error={errors.password}
          leftIcon={<Ionicons name="lock-closed-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
        />

        <Input
          label="Confirm Password"
          placeholder="Confirm your password"
          value={confirmPassword}
          onChangeText={setConfirmPassword}
          isPassword
          error={errors.confirmPassword}
          leftIcon={<Ionicons name="lock-closed-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
        />

        <TouchableOpacity
          style={styles.termsContainer}
          onPress={() => setAcceptTerms(!acceptTerms)}
        >
          <View style={[
            styles.checkbox,
            {
              backgroundColor: acceptTerms ? COLORS.primary : 'transparent',
              borderColor: acceptTerms ? COLORS.primary : isDark ? COLORS.ui.dark.border : COLORS.ui.light.border,
            }
          ]}>
            {acceptTerms && (
              <Ionicons name="checkmark" size={16} color="#FFFFFF" />
            )}
          </View>
          <Text style={[
            styles.termsText,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            I accept the Terms and Conditions
          </Text>
        </TouchableOpacity>

        {errors.terms && (
          <Text style={styles.termsError}>{errors.terms}</Text>
        )}

        <Button
          title="Register"
          onPress={handleRegister}
          isLoading={isLoading}
          style={styles.registerButton}
          size="large"
        />

        <Button
          title="Back"
          onPress={() => setStep(1)}
          variant="outline"
          style={styles.backButton}
        />
      </View>
    </>
  );

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={[
          styles.container,
          { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
        ]}
      >
        <View style={styles.logoContainer}>
          <Image
            source={require('../../assets/images/logo.png')}
            style={styles.logo}
            contentFit="contain"
          />
        </View>

        {step === 1 ? (
          renderUserTypeSelection()
        ) : userType === 'business' ? (
          renderBusinessForm()
        ) : (
          renderIndividualForm()
        )}

        {step === 1 && (
          <View style={styles.loginContainer}>
            <Text style={[
              styles.loginText,
              { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
            ]}>
              Already have an account?
            </Text>
            <Link href="/auth/login" asChild>
              <TouchableOpacity>
                <Text style={[
                  styles.loginLink,
                  { color: COLORS.primary }
                ]}>
                  Login
                </Text>
              </TouchableOpacity>
            </Link>
          </View>
        )}
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  logoContainer: {
    marginBottom: SPACING.lg,
  },
  logo: {
    width: 100,
    height: 100,
  },
  title: {
    fontSize: FONTS.sizes.xxl,
    fontFamily: FONTS.bold,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    marginBottom: SPACING.xl,
    textAlign: 'center',
  },
  userTypeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    maxWidth: 500,
    marginBottom: SPACING.xl,
  },
  userTypeCard: {
    width: '48%',
    padding: SPACING.lg,
    borderRadius: BORDER_RADIUS.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  userTypeIcon: {
    marginBottom: SPACING.md,
  },
  userTypeTitle: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.bold,
    marginBottom: SPACING.xs,
    textAlign: 'center',
  },
  userTypeDescription: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.regular,
    textAlign: 'center',
  },
  formContainer: {
    width: '100%',
    maxWidth: 400,
  },
  termsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderRadius: 4,
    marginRight: SPACING.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
  termsText: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.regular,
  },
  termsError: {
    color: COLORS.status.error,
    fontSize: FONTS.sizes.sm,
    marginBottom: SPACING.md,
    marginTop: -SPACING.xs,
  },
  registerButton: {
    marginTop: SPACING.md,
    marginBottom: SPACING.md,
  },
  backButton: {
    marginBottom: SPACING.xl,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: SPACING.md,
  },
  loginText: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    marginRight: SPACING.xs,
  },
  loginLink: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.bold,
  },
});
