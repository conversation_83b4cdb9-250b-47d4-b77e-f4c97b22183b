import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useEffect } from 'react';
import {
  Animated,
  Dimensions,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  useColorScheme,
  View,
} from 'react-native';
import { COLORS, FONTS, SHADOWS, SPACING } from '../../constants/theme';
import { useAuth } from '../../context/AuthContext';

export interface User {
  id: string;
  email: string;
  fullName: string;
  phoneNumber: string;
  user_type: 'individual' | 'business';
  // Add other user properties
}

export interface ApiError {
  message: string;
  status: number;
  errors?: Record<string, string[]>;
}

interface SidebarMenuItem {
  icon: string;
  label: string;
  route: string;
}

interface SidebarProps {
  isVisible: boolean;
  onClose: () => void;
}

const MENU_ITEMS: SidebarMenuItem[] = [
  { icon: 'home-outline', label: 'Home', route: '/home' },
  { icon: 'person-outline', label: 'My Profile', route: '/profile' },
  { icon: 'calculator-outline', label: 'Get Quote', route: '/(quotes)/quote' },
  { icon: 'shield-checkmark-outline', label: 'My Policies', route: '/(policies)/policies' },
  { icon: 'clipboard-outline', label: 'Claims', route: '/claims' },
  { icon: 'folder-outline', label: 'Documents', route: '/documents' },
  { icon: 'chatbubble-outline', label: 'Support/Live Chat', route: '/(support)/faq' },
  { icon: 'settings-outline', label: 'Settings', route: '/settings' },
  { icon: 'log-out-outline', label: 'Logout', route: '/logout' },
];

const { width, height } = Dimensions.get('window');

const Sidebar: React.FC<SidebarProps> = ({ isVisible, onClose }) => {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';
  const { signOut } = useAuth();
  
  const slideAnim = React.useRef(new Animated.Value(-width)).current;
  const opacityAnim = React.useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isVisible) {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: -width,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isVisible, slideAnim, opacityAnim]);

  const handleMenuItemPress = (route: string) => {
    onClose();
    
    if (route === '/logout') {
      signOut();
      return;
    }
    
    router.navigate(route);
  };

  if (!isVisible) {
    return null;
  }

  return (
    <View style={styles.container}>
      <Animated.View 
        style={[
          styles.backdrop,
          { opacity: opacityAnim }
        ]}
      >
        <TouchableOpacity 
          style={styles.backdropTouchable} 
          activeOpacity={1} 
          onPress={onClose} 
        />
      </Animated.View>
      
      <Animated.View 
        style={[
          styles.sidebarContainer,
          { 
            backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light,
            transform: [{ translateX: slideAnim }],
          }
        ]}
      >
        <View style={styles.header}>
          <Text style={[
            styles.headerTitle,
            { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
          ]}>
            Menu
          </Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons 
              name="close" 
              size={24} 
              color={isDark ? COLORS.text.dark.primary : COLORS.text.light.primary} 
            />
          </TouchableOpacity>
        </View>
        
        <ScrollView style={styles.menuItemsContainer}>
          {MENU_ITEMS.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.menuItem,
                { borderBottomColor: isDark ? COLORS.ui.dark.border : COLORS.ui.light.border },
                index === MENU_ITEMS.length - 1 && styles.logoutItem
              ]}
              onPress={() => handleMenuItemPress(item.route)}
            >
              <Ionicons 
                name={item.icon as any} 
                size={24} 
                color={
                  index === MENU_ITEMS.length - 1 
                    ? COLORS.status.error 
                    : isDark ? COLORS.text.dark.primary : COLORS.text.light.primary
                } 
              />
              <Text style={[
                styles.menuItemText,
                index === MENU_ITEMS.length - 1 && styles.logoutText,
                { color: index === MENU_ITEMS.length - 1 
                  ? COLORS.status.error 
                  : isDark ? COLORS.text.dark.primary : COLORS.text.light.primary 
                }
              ]}>
                {item.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1000,
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  backdropTouchable: {
    flex: 1,
  },
  sidebarContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: width * 0.8,
    height: height,
    ...SHADOWS.dark.medium,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: isDark ? COLORS.ui.dark.border : COLORS.ui.light.border,
  },
  headerTitle: {
    fontSize: FONTS.sizes.xl,
    fontFamily: FONTS.bold,
  },
  closeButton: {
    padding: SPACING.sm,
  },
  menuItemsContainer: {
    flex: 1,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    borderBottomWidth: 1,
  },
  menuItemText: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.medium,
    marginLeft: SPACING.md,
  },
  logoutItem: {
    marginTop: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  logoutText: {
    fontFamily: FONTS.bold,
  },
});

export default Sidebar;






