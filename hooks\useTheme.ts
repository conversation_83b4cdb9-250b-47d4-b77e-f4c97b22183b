import { useColorScheme } from 'react-native';
import { COLORS } from '../constants/theme';

/**
 * Custom hook for handling theme-related functionality
 * Provides consistent access to theme colors and dark/light mode detection
 */
export const useTheme = () => {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  // Background colors
  const backgroundColor = isDark ? COLORS.background.dark : COLORS.background.light;
  const cardColor = isDark ? COLORS.ui.dark.card : COLORS.ui.light.card;
  const inputColor = isDark ? COLORS.ui.dark.input : COLORS.ui.light.input;
  const borderColor = isDark ? COLORS.ui.dark.border : COLORS.ui.light.border;

  // Text colors
  const textPrimary = isDark ? COLORS.text.dark.primary : COLORS.text.light.primary;
  const textSecondary = isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary;
  const textTertiary = isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary;

  // Status colors
  const { success, warning, error, info } = COLORS.status;

  // Brand colors
  const { primary, secondary } = COLORS;

  return {
    isDark,
    colorScheme,
    colors: {
      background: backgroundColor,
      card: cardColor,
      input: inputColor,
      border: borderColor,
      textPrimary,
      textSecondary,
      textTertiary,
      primary,
      secondary,
      success,
      warning,
      error,
      info,
    },
  };
};
