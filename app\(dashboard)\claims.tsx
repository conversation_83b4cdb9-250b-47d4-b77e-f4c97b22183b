import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
  <PERSON>ert,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  useColorScheme,
  View,
} from 'react-native';
import Header from '../../components/ui/Header';
import Sidebar from '../../components/ui/Sidebar';
import { BORDER_RADIUS, COLORS, FONTS, SHADOWS, SPACING } from '../../constants/theme';

interface Claim {
  id: string;
  type: string;
  date: string;
  amount: string;
  status: 'pending' | 'approved' | 'rejected' | 'in_review';
  description: string;
  policyNumber: string;
}

const mockClaims: Claim[] = [
  {
    id: 'CLM-2023-001',
    type: 'Auto Insurance',
    date: '2023-10-15',
    amount: 'P5,200',
    status: 'approved',
    description: 'Minor damage to front bumper due to collision.',
    policyNumber: 'AUT-2023-45678',
  },
  {
    id: 'CLM-2023-002',
    type: 'Home Insurance',
    date: '2023-11-05',
    amount: 'P12,800',
    status: 'pending',
    description: 'Water damage from burst pipe in kitchen.',
    policyNumber: 'HOM-2023-98765',
  },
  {
    id: 'CLM-2023-003',
    type: 'Auto Insurance',
    date: '2023-09-22',
    amount: 'P3,500',
    status: 'in_review',
    description: 'Windshield replacement due to crack.',
    policyNumber: 'AUT-2023-45678',
  },
];

export default function Claims() {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';
  const [isSidebarVisible, setIsSidebarVisible] = useState(false);

  const handleCloseSidebar = () => {
    setIsSidebarVisible(false);
  };

  const getStatusColor = (status: Claim['status']) => {
    switch (status) {
      case 'approved':
        return COLORS.status.success;
      case 'pending':
        return COLORS.status.warning;
      case 'rejected':
        return COLORS.status.error;
      case 'in_review':
        return COLORS.status.info;
      default:
        return COLORS.status.info;
    }
  };

  const getStatusText = (status: Claim['status']) => {
    switch (status) {
      case 'approved':
        return 'Approved';
      case 'pending':
        return 'Pending';
      case 'rejected':
        return 'Rejected';
      case 'in_review':
        return 'In Review';
      default:
        return 'Unknown';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const handleNewClaim = () => {
    Alert.alert(
      'New Claim',
      'This feature would allow you to file a new claim in a real app.',
      [{ text: 'OK' }]
    );
  };

  const handleClaimPress = (claim: Claim) => {
    Alert.alert(
      'Claim Details',
      `View details for claim #${claim.id}`,
      [{ text: 'OK' }]
    );
  };

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
    ]}>
      <Sidebar
        isVisible={isSidebarVisible}
        onClose={handleCloseSidebar}
      />
      <Header
        title="Claims"
        showGreeting={false}
        onMenuPress={() => setIsSidebarVisible(true)}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {mockClaims.map((claim) => (
          <TouchableOpacity
            key={claim.id}
            style={[
              styles.claimItem,
              { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
            ]}
            onPress={() => handleClaimPress(claim)}
          >
            <View style={styles.claimHeader}>
              <Text style={[
                styles.claimType,
                { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
              ]}>
                {claim.type}
              </Text>

              <View style={[
                styles.statusBadge,
                { backgroundColor: getStatusColor(claim.status) }
              ]}>
                <Text style={styles.statusText}>
                  {getStatusText(claim.status)}
                </Text>
              </View>
            </View>

            <View style={styles.claimDetails}>
              <View style={styles.claimDetail}>
                <Text style={[
                  styles.claimLabel,
                  { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
                ]}>
                  Claim ID:
                </Text>
                <Text style={[
                  styles.claimValue,
                  { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
                ]}>
                  {claim.id}
                </Text>
              </View>

              <View style={styles.claimDetail}>
                <Text style={[
                  styles.claimLabel,
                  { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
                ]}>
                  Date:
                </Text>
                <Text style={[
                  styles.claimValue,
                  { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
                ]}>
                  {formatDate(claim.date)}
                </Text>
              </View>

              <View style={styles.claimDetail}>
                <Text style={[
                  styles.claimLabel,
                  { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
                ]}>
                  Amount:
                </Text>
                <Text style={[
                  styles.claimValue,
                  { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
                ]}>
                  {claim.amount}
                </Text>
              </View>
            </View>

            <Text style={[
              styles.claimDescription,
              { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
            ]}>
              {claim.description}
            </Text>

            <View style={styles.claimFooter}>
              <Text style={[
                styles.policyNumber,
                { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
              ]}>
                Policy: {claim.policyNumber}
              </Text>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
              />
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: SPACING.lg,
    paddingBottom: SPACING.xxl,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  title: {
    fontSize: FONTS.sizes.xl,
    fontFamily: FONTS.bold,
  },
  newClaimButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.sm,
  },
  newClaimButtonText: {
    color: '#FFFFFF',
    fontFamily: FONTS.medium,
    fontSize: FONTS.sizes.sm,
    marginLeft: SPACING.xs,
  },
  claimsContainer: {
    marginBottom: SPACING.lg,
  },
  claimItem: {
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    ...SHADOWS.light.small,
  },
  claimHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  claimType: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.bold,
  },
  statusBadge: {
    paddingVertical: SPACING.xs / 2,
    paddingHorizontal: SPACING.sm,
    borderRadius: BORDER_RADIUS.sm,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: FONTS.sizes.xs,
    fontFamily: FONTS.medium,
  },
  claimDetails: {
    marginBottom: SPACING.sm,
  },
  claimDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.xs / 2,
  },
  claimLabel: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.medium,
  },
  claimValue: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.regular,
  },
  claimDescription: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.regular,
    marginBottom: SPACING.sm,
  },
  claimFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: SPACING.xs,
  },
  policyNumber: {
    fontSize: FONTS.sizes.xs,
    fontFamily: FONTS.regular,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  emptyText: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.medium,
    marginTop: SPACING.md,
  },
  emptySubtext: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    marginTop: SPACING.xs,
  },
});

