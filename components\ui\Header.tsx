import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    Platform,
    SafeAreaView,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
    useColorScheme
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { COLORS } from '../../constants/theme';
import { useAuth } from '../../context/AuthContext';
import Sidebar from './Sidebar';

interface HeaderProps {
  title?: string;
  showBackButton?: boolean;
  showGreeting?: boolean;
  showNotification?: boolean;
  showMenu?: boolean;
  onBackPress?: () => void;
  onNotificationPress?: () => void;
  onMenuPress?: () => void;
  rightComponent?: React.ReactNode;
}

const Header: React.FC<HeaderProps> = ({
  title,
  showBackButton = true,
  showGreeting = false,
  showNotification = true,
  showMenu = true,
  onBackPress,
  onNotificationPress,
  onMenuPress,
  rightComponent,
}) => {
  const { user, signOut } = useAuth();
  const insets = useSafeAreaInsets();
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';
  const [isSidebarVisible, setIsSidebarVisible] = useState(false);

  // Get first name from username
  const firstName = user?.username?.split(' ')[0] || '';

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return { text: 'Good Morning', emoji: '🌅' };
    if (hour < 18) return { text: 'Good Afternoon', emoji: '☀️' };
    return { text: 'Good Evening', emoji: '🌙' };
  };

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      router.back();
    }
  };

  const handleNotificationPress = () => {
    if (onNotificationPress) {
      onNotificationPress();
    } else {
      console.log('Notification pressed');
      router.push('/(dashboard)/notifications');
    }
  };

  const handleMenuPress = () => {
    if (onMenuPress) {
      onMenuPress();
    } else {
      setIsSidebarVisible(true);
    }
  };

  const handleCloseSidebar = () => {
    setIsSidebarVisible(false);
  };

  const menuItems = [
    { icon: 'home-outline', title: 'Home', route: '/(dashboard)/home' },
    { icon: 'person-outline', title: 'My Profile', route: '/(dashboard)/profile' },
    { icon: 'calculator-outline', title: 'Get Quote', route: '/(dashboard)/quote' },
    { icon: 'shield-checkmark-outline', title: 'My Policies', route: '/(dashboard)/policies' },
    { icon: 'clipboard-outline', title: 'Claims', route: '/(dashboard)/claims' },
    { icon: 'folder-outline', title: 'Documents', route: '/(dashboard)/documents' },
    { icon: 'chatbubble-ellipses-outline', title: 'Support/Live Chat', route: '/(support)/support' },
    { icon: 'settings-outline', title: 'Settings', route: '/(dashboard)/settings' },
  ];

  return (
    <>
      <Sidebar
        isVisible={isSidebarVisible}
        onClose={handleCloseSidebar}
      />
      <SafeAreaView
        style={[
          styles.container,
          {
            paddingTop: Platform.OS === 'android' ? insets.top : 0,
            backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light,
            borderBottomColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'
          }
        ]}
      >
        <StatusBar
          barStyle={isDark ? 'light-content' : 'dark-content'}
          backgroundColor="transparent"
          translucent
        />
        <View style={styles.content}>
          {showBackButton && (
            <TouchableOpacity
              style={styles.backButton}
              onPress={handleBackPress}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Ionicons
                name="chevron-back"
                size={28}
                color={isDark ? COLORS.text.dark.primary : COLORS.text.light.primary}
              />
            </TouchableOpacity>
          )}

          {title && !showGreeting && (
            <Text
              style={[
                styles.title,
                { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
              ]}
            >
              {title}
            </Text>
          )}

          {showGreeting && (
            <View style={styles.greetingContainer}>
              <View style={styles.greetingRow}>
                <Text
                  style={[
                    styles.greeting,
                    { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
                  ]}
                >
                  {getGreeting().text}
                </Text>
                <Text style={styles.greetingEmoji}>{getGreeting().emoji}</Text>
              </View>
              <Text
                style={[
                  styles.name,
                  { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
                ]}
              >
                {firstName || 'User'}
              </Text>
            </View>
          )}

          <View style={styles.actions}>
            {rightComponent}

            {showNotification && (
              <TouchableOpacity
                style={[
                  styles.iconButton,
                  { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }
                ]}
                onPress={handleNotificationPress}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <View style={styles.notificationBadge} />
                <Ionicons
                  name="notifications-outline"
                  size={24}
                  color={isDark ? COLORS.text.dark.primary : COLORS.text.light.primary}
                />
              </TouchableOpacity>
            )}

            {showMenu && (
              <TouchableOpacity
                style={[
                  styles.iconButton,
                  { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }
                ]}
                onPress={handleMenuPress}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons
                  name="menu-outline"
                  size={28}
                  color={isDark ? COLORS.text.dark.primary : COLORS.text.light.primary}
                />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </SafeAreaView>

      {/* Sidebar is now used instead of the modal menu */}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  content: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    flex: 1,
    fontSize: 18,
    fontWeight: '700',
    color: '#1a1a1a',
    textAlign: 'center',
  },
  greetingContainer: {
    flex: 1,
  },
  greetingRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  greeting: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  greetingEmoji: {
    fontSize: 20,
    marginLeft: 8,
  },
  name: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1a1a1a',
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.05)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  notificationBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: COLORS.status.error,
    zIndex: 1,
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    zIndex: 2,
  },
  menuContainer: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 100 : 80,
    right: 16,
    width: 250,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
  },
  menuItemText: {
    marginLeft: 12,
    fontSize: 16,
    color: '#1a1a1a',
    fontWeight: '500',
  },
  logoutItem: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
    marginTop: 8,
  },
  logoutText: {
    color: COLORS.status.error,
  },
});

export default Header;
