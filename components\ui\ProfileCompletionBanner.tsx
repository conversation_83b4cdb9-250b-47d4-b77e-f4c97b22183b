import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  useColorScheme,
  View,
} from 'react-native';
import { BORDER_RADIUS, COLORS, FONTS, SHADOWS, SPACING } from '../../constants/theme';
import { useAuth } from '../../context/AuthContext';

interface ProfileCompletionBannerProps {
  onPress?: () => void;
}

const ProfileCompletionBanner: React.FC<ProfileCompletionBannerProps> = ({
  onPress,
}) => {
  const { user } = useAuth();
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  // Check if profile is complete
  const isProfileComplete = () => {
    if (!user) return false;

    // For individual users
    if (user.user_type === 'individual') {
      return !!(
        user.username &&
        user.email &&
        user.phone_number &&
        user.date_of_birth &&
        user.physical_address
      );
    }
    
    // For business users
    if (user.user_type === 'business') {
      return !!(
        user.username &&
        user.email &&
        user.phone_number &&
        user.business_name &&
        user.business_location &&
        user.business_address
      );
    }
    
    return false;
  };

  // If profile is complete, don't show the banner
  if (isProfileComplete()) {
    return null;
  }

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      // Navigate to profile page
      router.push('/profile');
      console.log('Navigating to profile page');
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card,
          ...(!isDark ? SHADOWS.light.medium : SHADOWS.dark.medium),
        },
      ]}
      onPress={handlePress}
    >
      <View style={styles.iconContainer}>
        <Ionicons
          name="alert-circle"
          size={24}
          color={COLORS.status.warning}
        />
      </View>
      
      <View style={styles.textContainer}>
        <Text style={[
          styles.title,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          Complete Your Profile
        </Text>
        
        <Text style={[
          styles.message,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          Please complete your profile to access all features.
        </Text>
      </View>
      
      <Ionicons
        name="chevron-forward"
        size={24}
        color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.md,
  },
  iconContainer: {
    marginRight: SPACING.md,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.bold,
    marginBottom: SPACING.xs,
  },
  message: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.regular,
  },
});

export default ProfileCompletionBanner;
