import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
    <PERSON>ert,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    useColorScheme,
    View,
} from 'react-native';
import { BORDER_RADIUS, COLORS, FONTS, SHADOWS, SPACING } from '../../constants/theme';

interface Notification {
  id: string;
  title: string;
  message: string;
  date: string;
  isRead: boolean;
  type: 'info' | 'warning' | 'success' | 'error';
}

const mockNotifications: Notification[] = [
  {
    id: '1',
    title: 'Welcome to Inerca',
    message: 'Thank you for choosing Inerca as your insurance partner. We are here to help you with all your insurance needs.',
    date: '2023-05-20T10:30:00',
    isRead: false,
    type: 'info',
  },
  {
    id: '2',
    title: 'Policy Renewal Reminder',
    message: 'Your auto insurance policy POL-123456 is due for renewal in 30 days. Please review your coverage and renew on time to avoid any lapse.',
    date: '2023-05-18T14:45:00',
    isRead: false,
    type: 'warning',
  },
  {
    id: '3',
    title: 'Claim Approved',
    message: 'Your claim #CL-789012 has been approved. The payment will be processed within 3-5 business days.',
    date: '2023-05-15T09:15:00',
    isRead: true,
    type: 'success',
  },
  {
    id: '4',
    title: 'New Policy Offer',
    message: 'We have a special offer on home insurance policies. Get 15% off when you purchase a new policy before the end of the month.',
    date: '2023-05-10T16:20:00',
    isRead: true,
    type: 'info',
  },
  {
    id: '5',
    title: 'Document Upload Required',
    message: 'Please upload your vehicle registration document to complete your auto insurance application.',
    date: '2023-05-05T11:00:00',
    isRead: true,
    type: 'error',
  },
];

export default function Notifications() {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications);

  const getTypeIcon = (type: Notification['type']) => {
    switch (type) {
      case 'info':
        return 'information-circle';
      case 'warning':
        return 'warning';
      case 'success':
        return 'checkmark-circle';
      case 'error':
        return 'alert-circle';
      default:
        return 'information-circle';
    }
  };

  const getTypeColor = (type: Notification['type']) => {
    switch (type) {
      case 'info':
        return COLORS.status.info;
      case 'warning':
        return COLORS.status.warning;
      case 'success':
        return COLORS.status.success;
      case 'error':
        return COLORS.status.error;
      default:
        return COLORS.status.info;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else {
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    }
  };

  const handleNotificationPress = (notification: Notification) => {
    // Mark as read
    if (!notification.isRead) {
      const updatedNotifications = notifications.map(n =>
        n.id === notification.id ? { ...n, isRead: true } : n
      );
      setNotifications(updatedNotifications);
    }

    // Show notification details
    Alert.alert(
      notification.title,
      notification.message,
      [{ text: 'OK' }]
    );
  };

  const handleMarkAllAsRead = () => {
    const updatedNotifications = notifications.map(n => ({ ...n, isRead: true }));
    setNotifications(updatedNotifications);
  };

  const handleClearAll = () => {
    Alert.alert(
      'Clear All Notifications',
      'Are you sure you want to clear all notifications?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          onPress: () => setNotifications([]),
          style: 'destructive',
        },
      ]
    );
  };

  const unreadCount = notifications.filter(n => !n.isRead).length;

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
    ]}>
      <Header
        title="Notifications"
        showGreeting={false}
        showNotification={false}
      />

      <View style={styles.actionsContainer}>
        {unreadCount > 0 && (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleMarkAllAsRead}
          >
            <Text style={[
              styles.actionButtonText,
              { color: COLORS.primary }
            ]}>
              Mark all as read
            </Text>
          </TouchableOpacity>
        )}

        {notifications.length > 0 && (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleClearAll}
          >
            <Text style={[
              styles.actionButtonText,
              { color: COLORS.status.error }
            ]}>
              Clear all
            </Text>
          </TouchableOpacity>
        )}
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {notifications.length > 0 ? (
          <View style={styles.notificationsContainer}>
            {notifications.map((notification) => (
              <TouchableOpacity
                key={notification.id}
                style={[
                  styles.notificationItem,
                  {
                    backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card,
                    opacity: notification.isRead ? 0.8 : 1,
                  }
                ]}
                onPress={() => handleNotificationPress(notification)}
              >
                <View style={[
                  styles.notificationIconContainer,
                  { backgroundColor: getTypeColor(notification.type) }
                ]}>
                  <Ionicons
                    name={getTypeIcon(notification.type)}
                    size={24}
                    color="#FFFFFF"
                  />
                </View>

                <View style={styles.notificationContent}>
                  <View style={styles.notificationHeader}>
                    <Text style={[
                      styles.notificationTitle,
                      { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
                    ]}>
                      {notification.title}
                    </Text>

                    <Text style={[
                      styles.notificationDate,
                      { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
                    ]}>
                      {formatDate(notification.date)}
                    </Text>
                  </View>

                  <Text
                    style={[
                      styles.notificationMessage,
                      { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
                    ]}
                    numberOfLines={2}
                  >
                    {notification.message}
                  </Text>
                </View>

                {!notification.isRead && (
                  <View style={styles.unreadIndicator} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons
              name="notifications-outline"
              size={64}
              color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
            />
            <Text style={[
              styles.emptyText,
              { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
            ]}>
              No notifications
            </Text>
            <Text style={[
              styles.emptySubtext,
              { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
            ]}>
              You don't have any notifications at the moment
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.sm,
  },
  actionButton: {
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    marginLeft: SPACING.md,
  },
  actionButtonText: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.medium,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: SPACING.lg,
    paddingTop: 0,
    paddingBottom: SPACING.xxl,
  },
  notificationsContainer: {
    marginBottom: SPACING.lg,
  },
  notificationItem: {
    flexDirection: 'row',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.md,
    ...SHADOWS.light.small,
  },
  notificationIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  notificationContent: {
    flex: 1,
  },
  notificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.xs,
  },
  notificationTitle: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.bold,
    flex: 1,
    marginRight: SPACING.sm,
  },
  notificationDate: {
    fontSize: FONTS.sizes.xs,
    fontFamily: FONTS.regular,
  },
  notificationMessage: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.regular,
    lineHeight: 20,
  },
  unreadIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: COLORS.primary,
    marginLeft: SPACING.sm,
    alignSelf: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xxl,
  },
  emptyText: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.bold,
    marginTop: SPACING.md,
    marginBottom: SPACING.xs,
  },
  emptySubtext: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    textAlign: 'center',
  },
});
