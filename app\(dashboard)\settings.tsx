import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Switch,
    Text,
    TouchableOpacity,
    useColorScheme,
    View,
} from 'react-native';
import { APP_VERSION } from '../../constants/config';
import { BORDER_RADIUS, COLORS, FONTS, SHADOWS, SPACING } from '../../constants/theme';
import { useAuth } from '../../context/AuthContext';

export default function Settings() {
  const { signOut } = useAuth();
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  // Settings state
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [emailNotificationsEnabled, setEmailNotificationsEnabled] = useState(true);
  const [biometricEnabled, setBiometricEnabled] = useState(false);
  const [language, setLanguage] = useState('English');

  const toggleNotifications = () => {
    setNotificationsEnabled(prev => !prev);
  };

  const toggleEmailNotifications = () => {
    setEmailNotificationsEnabled(prev => !prev);
  };

  const toggleBiometric = () => {
    setBiometricEnabled(prev => !prev);
    if (!biometricEnabled) {
      Alert.alert(
        'Biometric Authentication',
        'This would enable biometric authentication in a real app.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleLanguageChange = () => {
    Alert.alert(
      'Change Language',
      'Select a language',
      [
        { text: 'English', onPress: () => setLanguage('English') },
        { text: 'Setswana', onPress: () => setLanguage('Setswana') },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
    ]}>
      <EnhancedHeader
        title="Settings"
        showBackButton={false}
        showGreeting={false}
        showNotification={false}
        showMenu={false}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Notification Settings */}
        <View style={styles.section}>
          <Text style={[
            styles.sectionTitle,
            { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
          ]}>
            Notifications
          </Text>

          <View style={[
            styles.settingItem,
            { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
          ]}>
            <View style={styles.settingTextContainer}>
              <Text style={[
                styles.settingLabel,
                { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
              ]}>
                Push Notifications
              </Text>
              <Text style={[
                styles.settingDescription,
                { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
              ]}>
                Receive notifications on your device
              </Text>
            </View>
            <Switch
              value={notificationsEnabled}
              onValueChange={toggleNotifications}
              trackColor={{ false: '#767577', true: COLORS.primary }}
              thumbColor="#FFFFFF"
            />
          </View>

          <View style={[
            styles.settingItem,
            { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
          ]}>
            <View style={styles.settingTextContainer}>
              <Text style={[
                styles.settingLabel,
                { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
              ]}>
                Email Notifications
              </Text>
              <Text style={[
                styles.settingDescription,
                { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
              ]}>
                Receive notifications via email
              </Text>
            </View>
            <Switch
              value={emailNotificationsEnabled}
              onValueChange={toggleEmailNotifications}
              trackColor={{ false: '#767577', true: COLORS.primary }}
              thumbColor="#FFFFFF"
            />
          </View>
        </View>

        {/* App Settings */}
        <View style={styles.section}>
          <Text style={[
            styles.sectionTitle,
            { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
          ]}>
            App Settings
          </Text>

          <View style={[
            styles.settingItem,
            { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
          ]}>
            <View style={styles.settingTextContainer}>
              <Text style={[
                styles.settingLabel,
                { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
              ]}>
                Biometric Authentication
              </Text>
              <Text style={[
                styles.settingDescription,
                { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
              ]}>
                Use fingerprint or face ID to login
              </Text>
            </View>
            <Switch
              value={biometricEnabled}
              onValueChange={toggleBiometric}
              trackColor={{ false: '#767577', true: COLORS.primary }}
              thumbColor="#FFFFFF"
            />
          </View>

          <TouchableOpacity
            style={[
              styles.settingItem,
              { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
            ]}
            onPress={handleLanguageChange}
          >
            <View style={styles.settingTextContainer}>
              <Text style={[
                styles.settingLabel,
                { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
              ]}>
                Language
              </Text>
              <Text style={[
                styles.settingDescription,
                { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
              ]}>
                Change app language
              </Text>
            </View>
            <View style={styles.valueContainer}>
              <Text style={[
                styles.valueText,
                { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
              ]}>
                {language}
              </Text>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
              />
            </View>
          </TouchableOpacity>
        </View>

        {/* About & Legal */}
        <View style={styles.section}>
          <Text style={[
            styles.sectionTitle,
            { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
          ]}>
            About & Legal
          </Text>

          <TouchableOpacity
            style={[
              styles.settingItem,
              { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
            ]}
            onPress={() => router.push('/terms')}
          >
            <Text style={[
              styles.settingLabel,
              { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
            ]}>
              Terms & Conditions
            </Text>
            <Ionicons
              name="chevron-forward"
              size={20}
              color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.settingItem,
              { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
            ]}
            onPress={() => router.push('/privacy')}
          >
            <Text style={[
              styles.settingLabel,
              { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
            ]}>
              Privacy Policy
            </Text>
            <Ionicons
              name="chevron-forward"
              size={20}
              color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
            />
          </TouchableOpacity>

          <View style={[
            styles.settingItem,
            { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
          ]}>
            <Text style={[
              styles.settingLabel,
              { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
            ]}>
              App Version
            </Text>
            <Text style={[
              styles.versionText,
              { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
            ]}>
              {APP_VERSION}
            </Text>
          </View>
        </View>

        {/* Danger Zone */}
        <View style={styles.section}>
          <Text style={[
            styles.sectionTitle,
            { color: COLORS.status.error }
          ]}>
            Danger Zone
          </Text>

          <TouchableOpacity
            style={[
              styles.settingItem,
              { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
            ]}
            onPress={() => {
              Alert.alert(
                'Delete Account',
                'Are you sure you want to delete your account? This action cannot be undone.',
                [
                  { text: 'Cancel', style: 'cancel' },
                  {
                    text: 'Delete',
                    style: 'destructive',
                    onPress: () => {
                      console.log('Delete account requested');
                      Alert.alert(
                        'Account Deleted',
                        'Your account has been deleted successfully.',
                        [{ text: 'OK', onPress: signOut }]
                      );
                    }
                  }
                ]
              );
            }}
          >
            <View style={styles.settingTextContainer}>
              <Text style={[
                styles.settingLabel,
                { color: COLORS.status.error }
              ]}>
                Delete Account
              </Text>
              <Text style={[
                styles.settingDescription,
                { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
              ]}>
                Permanently delete your account and all data
              </Text>
            </View>
            <Ionicons
              name="trash-outline"
              size={24}
              color={COLORS.status.error}
            />
          </TouchableOpacity>
        </View>

        {/* Logout Button */}
        <TouchableOpacity
          style={[
            styles.logoutButton,
            { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
          ]}
          onPress={signOut}
        >
          <Ionicons
            name="log-out-outline"
            size={24}
            color={COLORS.status.error}
          />
          <Text style={[
            styles.logoutText,
            { color: COLORS.status.error }
          ]}>
            Logout
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: SPACING.lg,
    paddingBottom: SPACING.xxl,
  },
  section: {
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.bold,
    marginBottom: SPACING.sm,
    marginLeft: SPACING.xs,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.sm,
    ...SHADOWS.light.small,
  },
  settingTextContainer: {
    flex: 1,
  },
  settingLabel: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.medium,
  },
  settingDescription: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.regular,
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  valueText: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    marginRight: SPACING.xs,
  },
  versionText: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.regular,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    ...SHADOWS.light.small,
  },
  logoutText: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.medium,
    marginLeft: SPACING.sm,
  },
});
