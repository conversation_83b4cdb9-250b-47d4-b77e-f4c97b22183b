import React from 'react';
import {
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    useColorScheme,
    View,
} from 'react-native';
import { COLORS, FONTS, SPACING } from '../../constants/theme';

export default function PrivacyPolicy() {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
    ]}>
      <Header
        title="Privacy Policy"
        showGreeting={false}
        showNotification={false}
        showMenu={false}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Text style={[
          styles.title,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          Privacy Policy
        </Text>

        <Text style={[
          styles.date,
          { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
        ]}>
          Last Updated: June 1, 2023
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          Inerca Insurance ("us", "we", or "our") operates the Inerca mobile application (the "Service"). This page informs you of our policies regarding the collection, use, and disclosure of personal data when you use our Service and the choices you have associated with that data.
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          We use your data to provide and improve the Service. By using the Service, you agree to the collection and use of information in accordance with this policy.
        </Text>

        <Text style={[
          styles.sectionTitle,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          1. Information Collection and Use
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          We collect several different types of information for various purposes to provide and improve our Service to you.
        </Text>

        <Text style={[
          styles.subSectionTitle,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          1.1 Personal Data
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          While using our Service, we may ask you to provide us with certain personally identifiable information that can be used to contact or identify you ("Personal Data"). Personally identifiable information may include, but is not limited to:
        </Text>

        <View style={styles.bulletPointContainer}>
          <Text style={[
            styles.bulletPoint,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            • Email address
          </Text>
          <Text style={[
            styles.bulletPoint,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            • First name and last name
          </Text>
          <Text style={[
            styles.bulletPoint,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            • Phone number
          </Text>
          <Text style={[
            styles.bulletPoint,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            • Address, State, Province, ZIP/Postal code, City
          </Text>
          <Text style={[
            styles.bulletPoint,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            • Date of birth
          </Text>
          <Text style={[
            styles.bulletPoint,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            • Business information (for business accounts)
          </Text>
        </View>

        <Text style={[
          styles.subSectionTitle,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          1.2 Usage Data
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          We may also collect information that your browser sends whenever you visit our Service or when you access the Service by or through a mobile device ("Usage Data").
        </Text>

        <Text style={[
          styles.sectionTitle,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          2. Use of Data
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          Inerca Insurance uses the collected data for various purposes:
        </Text>

        <View style={styles.bulletPointContainer}>
          <Text style={[
            styles.bulletPoint,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            • To provide and maintain the Service
          </Text>
          <Text style={[
            styles.bulletPoint,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            • To notify you about changes to our Service
          </Text>
          <Text style={[
            styles.bulletPoint,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            • To allow you to participate in interactive features of our Service when you choose to do so
          </Text>
          <Text style={[
            styles.bulletPoint,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            • To provide customer care and support
          </Text>
          <Text style={[
            styles.bulletPoint,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            • To provide analysis or valuable information so that we can improve the Service
          </Text>
          <Text style={[
            styles.bulletPoint,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            • To monitor the usage of the Service
          </Text>
          <Text style={[
            styles.bulletPoint,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            • To detect, prevent and address technical issues
          </Text>
          <Text style={[
            styles.bulletPoint,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            • To process insurance applications, quotes, and claims
          </Text>
        </View>

        <Text style={[
          styles.sectionTitle,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          3. Security of Data
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          The security of your data is important to us, but remember that no method of transmission over the Internet, or method of electronic storage is 100% secure. While we strive to use commercially acceptable means to protect your Personal Data, we cannot guarantee its absolute security.
        </Text>

        <Text style={[
          styles.sectionTitle,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          4. Your Data Protection Rights
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          You have certain data protection rights. If you wish to be informed what Personal Data we hold about you and if you want it to be removed from our systems, please contact us.
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          In certain circumstances, you have the following data protection rights:
        </Text>

        <View style={styles.bulletPointContainer}>
          <Text style={[
            styles.bulletPoint,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            • The right to access, update or to delete the information we have on you
          </Text>
          <Text style={[
            styles.bulletPoint,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            • The right of rectification
          </Text>
          <Text style={[
            styles.bulletPoint,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            • The right to object
          </Text>
          <Text style={[
            styles.bulletPoint,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            • The right of restriction
          </Text>
          <Text style={[
            styles.bulletPoint,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            • The right to data portability
          </Text>
          <Text style={[
            styles.bulletPoint,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            • The right to withdraw consent
          </Text>
        </View>

        <Text style={[
          styles.sectionTitle,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          5. Contact Us
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: SPACING.lg,
    paddingBottom: SPACING.xxl,
  },
  title: {
    fontSize: FONTS.sizes.xxl,
    fontFamily: FONTS.bold,
    marginBottom: SPACING.sm,
  },
  date: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.regular,
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.bold,
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
  },
  subSectionTitle: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.bold,
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  paragraph: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    marginBottom: SPACING.md,
    lineHeight: 24,
  },
  bulletPointContainer: {
    marginLeft: SPACING.md,
    marginBottom: SPACING.md,
  },
  bulletPoint: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    marginBottom: SPACING.xs,
    lineHeight: 24,
  },
});
