import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
  Alert,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  useColorScheme,
  View,
} from 'react-native';
import Header from '../../components/ui/Header';
import Sidebar from '../../components/ui/Sidebar';
import { BORDER_RADIUS, COLORS, FONTS, SHADOWS, SPACING } from '../../constants/theme';

interface QuoteOption {
  id: string;
  type: string;
  icon: string;
  description: string;
  estimatedPremium: string;
}

const quoteOptions: QuoteOption[] = [
  {
    id: 'auto',
    type: 'Auto Insurance',
    icon: 'car-outline',
    description: 'Protect your vehicle with comprehensive coverage',
    estimatedPremium: 'From P200/month',
  },
  {
    id: 'home',
    type: 'Home Insurance',
    icon: 'home-outline',
    description: 'Secure your home and belongings',
    estimatedPremium: 'From P150/month',
  },
  {
    id: 'life',
    type: 'Life Insurance',
    icon: 'heart-outline',
    description: 'Provide financial security for your loved ones',
    estimatedPremium: 'From P300/month',
  },
  {
    id: 'business',
    type: 'Business Insurance',
    icon: 'business-outline',
    description: 'Protect your business operations and assets',
    estimatedPremium: 'From P500/month',
  },
];

export default function Quote() {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';
  const [isSidebarVisible, setIsSidebarVisible] = useState(false);
  const [selectedQuoteType, setSelectedQuoteType] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phoneNumber: '',
    additionalInfo: '',
  });

  const handleCloseSidebar = () => {
    setIsSidebarVisible(false);
  };

  const handleQuoteSelection = (quoteId: string) => {
    setSelectedQuoteType(quoteId);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmitQuote = () => {
    if (!selectedQuoteType) {
      Alert.alert('Error', 'Please select an insurance type');
      return;
    }

    if (!formData.fullName || !formData.email || !formData.phoneNumber) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    Alert.alert(
      'Quote Submitted',
      'Your quote request has been submitted. We will contact you within 24 hours.',
      [{ text: 'OK' }]
    );

    // Reset form
    setSelectedQuoteType(null);
    setFormData({
      fullName: '',
      email: '',
      phoneNumber: '',
      additionalInfo: '',
    });
  };

  const selectedOption = quoteOptions.find(option => option.id === selectedQuoteType);

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
    ]}>
      <Sidebar
        isVisible={isSidebarVisible}
        onClose={handleCloseSidebar}
      />
      <Header
        title="Get Quote"
        showGreeting={false}
        onMenuPress={() => setIsSidebarVisible(true)}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Text style={[
          styles.title,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          Get Your Insurance Quote
        </Text>

        <Text style={[
          styles.subtitle,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          Choose the type of insurance you're interested in
        </Text>

        <View style={styles.quoteOptionsContainer}>
          {quoteOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.quoteOption,
                {
                  backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card,
                  borderColor: selectedQuoteType === option.id ? COLORS.primary : 'transparent',
                  borderWidth: selectedQuoteType === option.id ? 2 : 0,
                }
              ]}
              onPress={() => handleQuoteSelection(option.id)}
            >
              <View style={[
                styles.quoteIconContainer,
                { backgroundColor: selectedQuoteType === option.id ? COLORS.primary : COLORS.secondary }
              ]}>
                <Ionicons name={option.icon as any} size={28} color="#FFFFFF" />
              </View>

              <View style={styles.quoteOptionInfo}>
                <Text style={[
                  styles.quoteOptionTitle,
                  { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
                ]}>
                  {option.type}
                </Text>

                <Text style={[
                  styles.quoteOptionDescription,
                  { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
                ]}>
                  {option.description}
                </Text>

                <Text style={[
                  styles.quoteOptionPremium,
                  { color: COLORS.primary }
                ]}>
                  {option.estimatedPremium}
                </Text>
              </View>

              {selectedQuoteType === option.id && (
                <Ionicons
                  name="checkmark-circle"
                  size={24}
                  color={COLORS.primary}
                  style={styles.selectedIcon}
                />
              )}
            </TouchableOpacity>
          ))}
        </View>

        {selectedQuoteType && (
          <View style={[
            styles.formContainer,
            { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
          ]}>
            <Text style={[
              styles.formTitle,
              { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
            ]}>
              Contact Information for {selectedOption?.type}
            </Text>

            <View style={styles.inputContainer}>
              <Text style={[
                styles.inputLabel,
                { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
              ]}>
                Full Name *
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  {
                    backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light,
                    color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary,
                    borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
                  }
                ]}
                value={formData.fullName}
                onChangeText={(value) => handleInputChange('fullName', value)}
                placeholder="Enter your full name"
                placeholderTextColor={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={[
                styles.inputLabel,
                { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
              ]}>
                Email Address *
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  {
                    backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light,
                    color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary,
                    borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
                  }
                ]}
                value={formData.email}
                onChangeText={(value) => handleInputChange('email', value)}
                placeholder="Enter your email address"
                placeholderTextColor={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={[
                styles.inputLabel,
                { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
              ]}>
                Phone Number *
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  {
                    backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light,
                    color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary,
                    borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
                  }
                ]}
                value={formData.phoneNumber}
                onChangeText={(value) => handleInputChange('phoneNumber', value)}
                placeholder="Enter your phone number"
                placeholderTextColor={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
                keyboardType="phone-pad"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={[
                styles.inputLabel,
                { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
              ]}>
                Additional Information
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  styles.textArea,
                  {
                    backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light,
                    color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary,
                    borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
                  }
                ]}
                value={formData.additionalInfo}
                onChangeText={(value) => handleInputChange('additionalInfo', value)}
                placeholder="Any additional information or specific requirements"
                placeholderTextColor={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>

            <TouchableOpacity
              style={[styles.submitButton, { backgroundColor: COLORS.primary }]}
              onPress={handleSubmitQuote}
            >
              <Text style={styles.submitButtonText}>Submit Quote Request</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: SPACING.lg,
    paddingBottom: SPACING.xxl,
  },
  title: {
    fontSize: FONTS.sizes.xxl,
    fontFamily: FONTS.bold,
    marginBottom: SPACING.xs,
  },
  subtitle: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    marginBottom: SPACING.lg,
  },
  quoteOptionsContainer: {
    marginBottom: SPACING.lg,
  },
  quoteOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.md,
    ...SHADOWS.light.small,
  },
  quoteIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  quoteOptionInfo: {
    flex: 1,
  },
  quoteOptionTitle: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.bold,
    marginBottom: SPACING.xs,
  },
  quoteOptionDescription: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.regular,
    marginBottom: SPACING.xs,
  },
  quoteOptionPremium: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.bold,
  },
  selectedIcon: {
    marginLeft: SPACING.sm,
  },
  formContainer: {
    padding: SPACING.lg,
    borderRadius: BORDER_RADIUS.md,
    ...SHADOWS.light.small,
  },
  formTitle: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.bold,
    marginBottom: SPACING.lg,
  },
  inputContainer: {
    marginBottom: SPACING.md,
  },
  inputLabel: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.medium,
    marginBottom: SPACING.xs,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: BORDER_RADIUS.sm,
    padding: SPACING.md,
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
  },
  textArea: {
    height: 100,
  },
  submitButton: {
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.sm,
    alignItems: 'center',
    marginTop: SPACING.md,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.bold,
  },
});
