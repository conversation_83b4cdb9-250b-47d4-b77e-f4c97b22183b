import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useRef, useState } from 'react';
import {
    Dimensions,
    Image,
    NativeScrollEvent,
    NativeSyntheticEvent,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    useColorScheme,
    View
} from 'react-native';
import { BORDER_RADIUS, COLORS, FONTS, SPACING } from '../../constants/theme';

interface CarouselProps {
  data: CarouselItem[];
  gap?: number;
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showPagination?: boolean;
  onItemPress?: (item: CarouselItem) => void;
  type?: 'banner' | 'policy';
  direction?: 'forward' | 'backward';
}

export interface CarouselItem {
  id: string;
  title: string;
  description?: string;
  image?: any;
  backgroundColor?: string;
  icon?: string;
  actionText?: string;
  policyNumber?: string;
  expiryDate?: string;
  coverageType?: string;
}

const Carousel: React.FC<CarouselProps> = ({
  data,
  gap = 16,
  autoPlay = false,
  autoPlayInterval = 5000,
  showPagination = true,
  onItemPress,
  type = 'banner',
  direction = 'forward'
}) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);
  const windowWidth = Dimensions.get('window').width;
  // Make the card width slightly smaller to ensure it fits properly on all screens
  const itemWidth = windowWidth - 40; // Increased padding for better fit
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  useEffect(() => {
    let interval: ReturnType<typeof setInterval>;
    if (autoPlay) {
      interval = setInterval(() => {
        let nextIndex;
        if (direction === 'forward') {
          nextIndex = (activeIndex + 1) % data.length;
        } else {
          nextIndex = activeIndex - 1;
          if (nextIndex < 0) nextIndex = data.length - 1;
        }

        // Use a slower animation for smoother transitions
        scrollViewRef.current?.scrollTo({
          x: nextIndex * (itemWidth + gap),
          animated: true,
        });
        setActiveIndex(nextIndex);
      }, autoPlayInterval);
    }
    return () => clearInterval(interval);
  }, [activeIndex, autoPlay, autoPlayInterval, data.length, itemWidth, gap, direction]);

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const contentOffset = event.nativeEvent.contentOffset;
    const index = Math.round(contentOffset.x / (itemWidth + gap));
    setActiveIndex(index);
  };

  const renderBannerItem = (item: CarouselItem) => {
    return (
      <TouchableOpacity
        style={[
          styles.bannerItem,
          {
            backgroundColor: item.backgroundColor || COLORS.primary
          }
        ]}
        onPress={() => onItemPress && onItemPress(item)}
        activeOpacity={0.9}
      >
        <View style={styles.bannerContent}>
          <View style={styles.bannerTextContainer}>
            <Text style={styles.bannerTitle}>{item.title}</Text>
            {item.description && (
              <Text style={styles.bannerDescription}>{item.description}</Text>
            )}
            {item.actionText && (
              <View style={styles.bannerActionButton}>
                <Text style={styles.bannerActionText}>{item.actionText}</Text>
              </View>
            )}
          </View>
          {item.image && (
            <Image
              source={item.image}
              style={styles.bannerImage}
              resizeMode="contain"
            />
          )}
          {!item.image && item.icon && (
            <Ionicons name={item.icon as any} size={32} color="#FFFFFF" />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const renderPolicyItem = (item: CarouselItem) => {
    return (
      <TouchableOpacity
        style={[
          styles.policyItem,
          {
            backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card
          }
        ]}
        onPress={() => onItemPress && onItemPress(item)}
        activeOpacity={0.9}
      >
        <View style={styles.policyHeader}>
          {item.icon && (
            <Ionicons
              name={item.icon as any}
              size={24}
              color={item.backgroundColor || COLORS.primary}
            />
          )}
          <Text style={[
            styles.policyTitle,
            { color: item.backgroundColor || COLORS.primary }
          ]}>
            {item.title}
          </Text>
        </View>

        {item.policyNumber && (
          <Text style={[
            styles.policyNumber,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            Policy #: {item.policyNumber}
          </Text>
        )}

        {item.coverageType && (
          <View style={styles.policyDetails}>
            <Text style={[
              styles.policyLabel,
              { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
            ]}>
              Coverage:
            </Text>
            <Text style={[
              styles.policyValue,
              { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
            ]}>
              {item.coverageType}
            </Text>
          </View>
        )}

        {item.expiryDate && (
          <View style={styles.policyDetails}>
            <Text style={[
              styles.policyLabel,
              { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
            ]}>
              Expires:
            </Text>
            <Text style={[
              styles.policyValue,
              { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
            ]}>
              {item.expiryDate}
            </Text>
          </View>
        )}

        {item.description && !item.coverageType && (
          <Text style={[
            styles.policyDescription,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            {item.description}
          </Text>
        )}

        {item.actionText && (
          <TouchableOpacity
            style={[
              styles.policyButton,
              { backgroundColor: item.backgroundColor || COLORS.primary }
            ]}
            onPress={() => onItemPress && onItemPress(item)}
          >
            <Text style={styles.policyButtonText}>{item.actionText}</Text>
          </TouchableOpacity>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        snapToInterval={itemWidth + gap}
        decelerationRate="normal" // Changed from "fast" to "normal" for smoother scrolling
        contentContainerStyle={[
          styles.scrollContent,
          { gap }
        ]}
        onScroll={handleScroll}
        scrollEventThrottle={32} // Increased for smoother scrolling
      >
        {data.map((item, index) => (
          <View key={index} style={[styles.itemContainer, { width: itemWidth }]}>
            {type === 'banner'
              ? renderBannerItem(item)
              : renderPolicyItem(item)
            }
          </View>
        ))}
      </ScrollView>

      {showPagination && data.length > 1 && (
        <View style={styles.pagination}>
          {data.map((_, index) => (
            <View
              key={index}
              style={[
                styles.paginationDot,
                index === activeIndex && styles.paginationDotActive
              ]}
            />
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  scrollContent: {
    paddingHorizontal: 16,
  },
  itemContainer: {
    overflow: 'hidden',
  },
  bannerItem: {
    flex: 1,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.lg,
    overflow: 'hidden',
    borderBlockColor: 'rgba(255,255,255,0.2)',
    height: 180,
  },
  bannerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: '100%',
  },
  bannerTextContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  bannerTitle: {
    color: '#FFFFFF',
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.bold,
    marginBottom: SPACING.xs,
  },
  bannerDescription: {
    color: '#FFFFFF',
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    marginBottom: SPACING.md,
  },
  bannerActionButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.sm,
    alignSelf: 'flex-start',
    marginTop: SPACING.sm,
  },
  bannerActionText: {
    color: '#FFFFFF',
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.medium,
  },
  bannerImage: {
    width: 80,
    height: 80,
  },
  policyItem: {
    flex: 1,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.lg,
    overflow: 'hidden',
    height: 200,
    justifyContent: 'space-between',
  },
  policyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  policyTitle: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.bold,
    marginLeft: SPACING.xs,
  },
  policyNumber: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.regular,
    marginBottom: SPACING.md,
  },
  policyDescription: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    marginBottom: SPACING.md,
    marginTop: SPACING.sm,
  },
  policyDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.xs,
  },
  policyLabel: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.medium,
  },
  policyValue: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.regular,
  },
  policyButton: {
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.sm,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    alignItems: 'center',
    marginTop: SPACING.md,
  },
  policyButtonText: {
    color: '#FFFFFF',
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.medium,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
    gap: 8,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(0,0,0,0.2)',
  },
  paginationDotActive: {
    backgroundColor: '#4A90E2',
    transform: [{ scale: 1.2 }],
  },
});

export default Carousel;
