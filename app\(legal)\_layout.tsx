import { Stack, router } from 'expo-router';
import React, { useEffect } from 'react';
import { useColorScheme } from 'react-native';
import LiveChat from '../../components/shared/LiveChat';
import { COLORS } from '../../constants/theme';
import { useAuth } from '../../context/AuthContext';

export default function LegalLayout() {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';
  const { user, isLoading } = useAuth();

  // Check if user is authenticated
  useEffect(() => {
    if (!isLoading && !user) {
      // Redirect to login if not authenticated
      router.replace('/(auth)/login');
    }
  }, [user, isLoading]);

  // Don't render anything while checking authentication
  if (isLoading) {
    return null;
  }

  return (
    <>
      <Stack
        screenOptions={{
          headerShown: false,
          contentStyle: {
            backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light,
          },
        }}
      >
        <Stack.Screen name="terms" />
        <Stack.Screen name="privacy" />
      </Stack>

      {/* Live Chat component that appears on all screens */}
      <LiveChat />
    </>
  );
}
