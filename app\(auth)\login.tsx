import { Ionicons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import { Link } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  useColorScheme,
  View,
} from 'react-native';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import { COLORS, FONTS, SPACING } from '../../constants/theme';
import { useAuth } from '../../context/AuthContext';

export default function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{ email?: string; password?: string }>({});
  const { signIn } = useAuth();
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  const validateForm = () => {
    const newErrors: { email?: string; password?: string } = {};

    // Email validation
    if (!email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email is invalid';
    }

    // Password validation
    if (!password) {
      newErrors.password = 'Password is required';
    } else if (password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // Log the login details including rememberMe state
      console.log('Login attempt:', { email, password, rememberMe });

      await signIn(email, password);
      // Navigation is handled in the signIn function
    } catch (error) {
      Alert.alert(
        'Login Failed',
        'Invalid email or password. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={[
          styles.container,
          { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
        ]}
      >
        <View style={styles.logoContainer}>
          <Image
            source={require('../../assets/images/logo.png')}
            style={styles.logo}
            contentFit="contain"
          />
        </View>

        <Text style={[
          styles.title,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          Welcome Back
        </Text>

        <Text style={[
          styles.subtitle,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          Sign in to continue to Inerca
        </Text>

        <View style={styles.formContainer}>
          <Input
            label="Email"
            placeholder="Enter your email"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            error={errors.email}
            leftIcon={<Ionicons name="mail-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
          />

          <Input
            label="Password"
            placeholder="Enter your password"
            value={password}
            onChangeText={setPassword}
            isPassword
            error={errors.password}
            leftIcon={<Ionicons name="lock-closed-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
          />

          <View style={styles.optionsContainer}>
            <TouchableOpacity
              style={styles.rememberMeContainer}
              onPress={() => setRememberMe(!rememberMe)}
            >
              <View style={[
                styles.checkbox,
                {
                  backgroundColor: rememberMe ? COLORS.primary : 'transparent',
                  borderColor: rememberMe ? COLORS.primary : isDark ? COLORS.ui.dark.border : COLORS.ui.light.border,
                }
              ]}>
                {rememberMe && (
                  <Ionicons name="checkmark" size={16} color="#FFFFFF" />
                )}
              </View>
              <Text style={[
                styles.checkboxText,
                { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
              ]}>
                Remember Me
              </Text>
            </TouchableOpacity>

            <Link href="/auth/forgot-password" asChild>
              <TouchableOpacity>
                <Text style={[
                  styles.forgotPasswordText,
                  { color: COLORS.primary }
                ]}>
                  Forgot Password?
                </Text>
              </TouchableOpacity>
            </Link>
          </View>

          <TouchableOpacity
            style={styles.biometricContainer}
            onPress={() => {
              console.log('Biometric authentication requested');
              Alert.alert('Biometric Authentication', 'This feature would use device biometrics in a real app.');
            }}
          >
            <Ionicons
              name="finger-print-outline"
              size={24}
              color={isDark ? COLORS.text.dark.primary : COLORS.text.light.primary}
            />
            <Text style={[
              styles.biometricText,
              { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
            ]}>
              Login with Biometrics
            </Text>
          </TouchableOpacity>

          <Button
            title="Login"
            onPress={handleLogin}
            isLoading={isLoading}
            style={styles.loginButton}
            size="large"
          />

          <View style={styles.registerContainer}>
            <Text style={[
              styles.registerText,
              { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
            ]}>
              Don't have an account?
            </Text>
            <Link href="/auth/register" asChild>
              <TouchableOpacity>
                <Text style={[
                  styles.registerLink,
                  { color: COLORS.primary }
                ]}>
                  Register
                </Text>
              </TouchableOpacity>
            </Link>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  logoContainer: {
    marginBottom: SPACING.xl,
  },
  logo: {
    width: 120,
    height: 120,
  },
  title: {
    fontSize: FONTS.sizes.xxl,
    fontFamily: FONTS.bold,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    marginBottom: SPACING.xl,
    textAlign: 'center',
  },
  formContainer: {
    width: '100%',
    maxWidth: 400,
  },
  optionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  rememberMeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderRadius: 4,
    marginRight: SPACING.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxText: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.regular,
  },
  forgotPasswordText: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.medium,
  },
  biometricContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.lg,
  },
  biometricText: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.medium,
    marginLeft: SPACING.sm,
  },
  loginButton: {
    marginTop: SPACING.md,
    marginBottom: SPACING.xl,
  },
  registerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: SPACING.lg,
  },
  registerText: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    marginRight: SPACING.xs,
  },
  registerLink: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.bold,
  },
});
