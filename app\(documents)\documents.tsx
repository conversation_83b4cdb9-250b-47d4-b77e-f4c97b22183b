import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
  <PERSON>ert,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  useColorScheme,
  View,
} from 'react-native';
import Header from '../../components/ui/Header';
import { BORDER_RADIUS, COLORS, FONTS, SHADOWS, SPACING } from '../../constants/theme';

interface Document {
  id: string;
  name: string;
  type: string;
  date: string;
  size: string;
  icon: string;
}

const mockDocuments: Document[] = [
  {
    id: '1',
    name: 'Policy_Document_Auto.pdf',
    type: 'PDF',
    date: '2023-05-15',
    size: '1.2 MB',
    icon: 'document-text',
  },
  {
    id: '2',
    name: 'Insurance_Certificate.pdf',
    type: 'PDF',
    date: '2023-04-20',
    size: '0.8 MB',
    icon: 'document-text',
  },
  {
    id: '3',
    name: 'Claim_Receipt_123456.jpg',
    type: 'Image',
    date: '2023-03-10',
    size: '2.5 MB',
    icon: 'image',
  },
  {
    id: '4',
    name: 'Policy_Terms_and_Conditions.pdf',
    type: 'PDF',
    date: '2023-02-28',
    size: '1.5 MB',
    icon: 'document-text',
  },
];

export default function Documents() {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';
  const [documents, setDocuments] = useState<Document[]>(mockDocuments);

  const handleDocumentPress = (document: Document) => {
    Alert.alert(
      'Document Action',
      `What would you like to do with ${document.name}?`,
      [
        { text: 'View', onPress: () => console.log(`Viewing ${document.name}`) },
        { text: 'Share', onPress: () => console.log(`Sharing ${document.name}`) },
        { text: 'Download', onPress: () => console.log(`Downloading ${document.name}`) },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleUpload = () => {
    Alert.alert(
      'Upload Document',
      'This feature would allow you to upload documents in a real app.',
      [{ text: 'OK' }]
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
    ]}>
      <Header
        title="Documents"
        showGreeting={false}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.headerContainer}>
          <Text style={[
            styles.title,
            { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
          ]}>
            My Documents
          </Text>

          <TouchableOpacity
            style={[styles.uploadButton, { backgroundColor: COLORS.primary }]}
            onPress={handleUpload}
          >
            <Ionicons name="cloud-upload-outline" size={20} color="#FFFFFF" />
            <Text style={styles.uploadButtonText}>Upload</Text>
          </TouchableOpacity>
        </View>

        {documents.length > 0 ? (
          <View style={styles.documentsContainer}>
            {documents.map((document) => (
              <TouchableOpacity
                key={document.id}
                style={[
                  styles.documentItem,
                  { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
                ]}
                onPress={() => handleDocumentPress(document)}
              >
                <View style={[
                  styles.documentIconContainer,
                  { backgroundColor: document.type === 'PDF' ? COLORS.primary : COLORS.secondary }
                ]}>
                  <Ionicons name={document.icon as any} size={24} color="#FFFFFF" />
                </View>

                <View style={styles.documentInfo}>
                  <Text style={[
                    styles.documentName,
                    { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
                  ]}>
                    {document.name}
                  </Text>

                  <View style={styles.documentDetails}>
                    <Text style={[
                      styles.documentDetail,
                      { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
                    ]}>
                      {document.type}
                    </Text>
                    <Text style={[
                      styles.documentDetail,
                      { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
                    ]}>
                      {formatDate(document.date)}
                    </Text>
                    <Text style={[
                      styles.documentDetail,
                      { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
                    ]}>
                      {document.size}
                    </Text>
                  </View>
                </View>

                <TouchableOpacity
                  style={styles.documentAction}
                  onPress={() => handleDocumentPress(document)}
                >
                  <Ionicons
                    name="ellipsis-vertical"
                    size={20}
                    color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
                  />
                </TouchableOpacity>
              </TouchableOpacity>
            ))}
          </View>
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons
              name="document-outline"
              size={64}
              color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
            />
            <Text style={[
              styles.emptyText,
              { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
            ]}>
              No documents found
            </Text>
            <Text style={[
              styles.emptySubtext,
              { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
            ]}>
              Upload documents to see them here
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: SPACING.lg,
    paddingBottom: SPACING.xxl,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  title: {
    fontSize: FONTS.sizes.xl,
    fontFamily: FONTS.bold,
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  uploadButtonText: {
    color: '#FFFFFF',
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.medium,
    marginLeft: SPACING.xs,
  },
  documentsContainer: {
    marginBottom: SPACING.lg,
  },
  documentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.md,
    ...SHADOWS.light.small,
  },
  documentIconContainer: {
    width: 48,
    height: 48,
    borderRadius: BORDER_RADIUS.sm,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  documentInfo: {
    flex: 1,
  },
  documentName: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.medium,
    marginBottom: SPACING.xs,
  },
  documentDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  documentDetail: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.regular,
    marginRight: SPACING.md,
  },
  documentAction: {
    padding: SPACING.xs,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xxl,
  },
  emptyText: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.bold,
    marginTop: SPACING.md,
    marginBottom: SPACING.xs,
  },
  emptySubtext: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    textAlign: 'center',
  },
});
