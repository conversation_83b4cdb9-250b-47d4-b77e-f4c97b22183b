import Header from '@/components/ui/Header';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React from 'react';
import {
  Alert,
  Image,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  useColorScheme,
  View,
} from 'react-native';
import { BORDER_RADIUS, COLORS, FONTS, SHADOWS, SPACING } from '../../constants/theme';
import { useAuth } from '../../context/AuthContext';

export default function Profile() {
  const { user, signOut } = useAuth();
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  const handleEditProfile = () => {
    router.push('/profile/edit');
  };

  const handleUploadDocument = () => {
    Alert.alert(
      'Upload Document',
      'This feature will allow you to upload KYC documents.',
      [{ text: 'OK' }]
    );
  };

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
    ]}>
      <Header
        title="My Profile"
        showBackButton={false}
        showGreeting={false}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Profile Header */}
        <View style={[
          styles.profileHeader,
          {
            backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card,
            ...(!isDark ? SHADOWS.light.medium : SHADOWS.dark.medium)
          }
        ]}>
          <View style={styles.profileImageContainer}>
            <Image
              source={require('../../assets/images/logo.png')}
              style={styles.profileImage}
            />
            <TouchableOpacity style={styles.editImageButton}>
              <Ionicons name="camera" size={20} color="#FFFFFF" />
            </TouchableOpacity>
          </View>

          <Text style={[
            styles.profileName,
            { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
          ]}>
            {user?.username || 'User'}
          </Text>

          <Text style={[
            styles.profileType,
            { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
          ]}>
            {user?.user_type === 'business' ? 'Business Account' : 'Individual Account'}
          </Text>

          <TouchableOpacity
            style={[styles.editButton, { backgroundColor: COLORS.primary }]}
            onPress={handleEditProfile}
          >
            <Text style={styles.editButtonText}>Edit Profile</Text>
          </TouchableOpacity>
        </View>

        {/* Personal Information */}
        <View style={[
          styles.section,
          {
            backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card,
            ...(!isDark ? SHADOWS.light.small : SHADOWS.dark.small)
          }
        ]}>
          <Text style={[
            styles.sectionTitle,
            { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
          ]}>
            Personal Information
          </Text>

          <View style={styles.infoRow}>
            <Ionicons
              name="mail-outline"
              size={20}
              color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
            />
            <Text style={[
              styles.infoLabel,
              { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
            ]}>
              Email:
            </Text>
            <Text style={[
              styles.infoValue,
              { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
            ]}>
              {user?.email || 'Not provided'}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Ionicons
              name="call-outline"
              size={20}
              color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
            />
            <Text style={[
              styles.infoLabel,
              { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
            ]}>
              Phone:
            </Text>
            <Text style={[
              styles.infoValue,
              { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
            ]}>
              {user?.phone_number || 'Not provided'}
            </Text>
          </View>

          {user?.user_type === 'individual' && (
            <>
              <View style={styles.infoRow}>
                <Ionicons
                  name="calendar-outline"
                  size={20}
                  color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
                />
                <Text style={[
                  styles.infoLabel,
                  { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
                ]}>
                  Date of Birth:
                </Text>
                <Text style={[
                  styles.infoValue,
                  { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
                ]}>
                  {user?.date_of_birth || 'Not provided'}
                </Text>
              </View>

              <View style={styles.infoRow}>
                <Ionicons
                  name="briefcase-outline"
                  size={20}
                  color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
                />
                <Text style={[
                  styles.infoLabel,
                  { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
                ]}>
                  Occupation:
                </Text>
                <Text style={[
                  styles.infoValue,
                  { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
                ]}>
                  {user?.occupation || 'Not provided'}
                </Text>
              </View>
            </>
          )}

          {user?.user_type === 'business' && (
            <>
              <View style={styles.infoRow}>
                <Ionicons
                  name="business-outline"
                  size={20}
                  color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
                />
                <Text style={[
                  styles.infoLabel,
                  { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
                ]}>
                  Business Name:
                </Text>
                <Text style={[
                  styles.infoValue,
                  { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
                ]}>
                  {user?.business_name || 'Not provided'}
                </Text>
              </View>

              <View style={styles.infoRow}>
                <Ionicons
                  name="location-outline"
                  size={20}
                  color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
                />
                <Text style={[
                  styles.infoLabel,
                  { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
                ]}>
                  Business Location:
                </Text>
                <Text style={[
                  styles.infoValue,
                  { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
                ]}>
                  {user?.business_location || 'Not provided'}
                </Text>
              </View>
            </>
          )}

          <View style={styles.infoRow}>
            <Ionicons
              name="home-outline"
              size={20}
              color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
            />
            <Text style={[
              styles.infoLabel,
              { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
            ]}>
              {user?.user_type === 'business' ? 'Business Address:' : 'Address:'}
            </Text>
            <Text style={[
              styles.infoValue,
              { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
            ]}>
              {user?.user_type === 'business'
                ? (user?.business_address || 'Not provided')
                : (user?.physical_address || 'Not provided')}
            </Text>
          </View>
        </View>

        {/* KYC Documents */}
        <View style={[
          styles.section,
          {
            backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card,
            ...(!isDark ? SHADOWS.light.small : SHADOWS.dark.small)
          }
        ]}>
          <Text style={[
            styles.sectionTitle,
            { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
          ]}>
            KYC Documents
          </Text>

          <TouchableOpacity
            style={[styles.uploadButton, { backgroundColor: COLORS.secondary }]}
            onPress={handleUploadDocument}
          >
            <Ionicons name="cloud-upload-outline" size={20} color="#FFFFFF" />
            <Text style={styles.uploadButtonText}>Upload New Document</Text>
          </TouchableOpacity>

          <Text style={[
            styles.noDocumentsText,
            { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
          ]}>
            No documents uploaded yet.
          </Text>
        </View>

        {/* Logout Button */}
        <TouchableOpacity
          style={[
            styles.logoutButton,
            {
              backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card,
              ...(!isDark ? SHADOWS.light.small : SHADOWS.dark.small)
            }
          ]}
          onPress={signOut}
        >
          <Ionicons
            name="log-out-outline"
            size={24}
            color={COLORS.status.error}
          />
          <Text style={[
            styles.logoutText,
            { color: COLORS.status.error }
          ]}>
            Logout
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: SPACING.lg,
    paddingBottom: SPACING.xxl,
  },
  profileHeader: {
    alignItems: 'center',
    padding: SPACING.lg,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.md,
  },
  profileImageContainer: {
    position: 'relative',
    marginBottom: SPACING.md,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#F0F0F0',
  },
  editImageButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: COLORS.primary,
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#FFFFFF',
  },
  profileName: {
    fontSize: FONTS.sizes.xl,
    fontFamily: FONTS.bold,
    marginBottom: SPACING.xs,
  },
  profileType: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    marginBottom: SPACING.md,
  },
  editButton: {
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.lg,
    borderRadius: BORDER_RADIUS.md,
  },
  editButtonText: {
    color: '#FFFFFF',
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.medium,
  },
  section: {
    padding: SPACING.lg,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.md,
  },
  sectionTitle: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.bold,
    marginBottom: SPACING.md,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  infoLabel: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.medium,
    marginLeft: SPACING.sm,
    width: 120,
  },
  infoValue: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    flex: 1,
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.md,
  },
  uploadButtonText: {
    color: '#FFFFFF',
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.medium,
    marginLeft: SPACING.xs,
  },
  noDocumentsText: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    marginTop: SPACING.md,
  },
  logoutText: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.medium,
    marginLeft: SPACING.sm,
  },
});
