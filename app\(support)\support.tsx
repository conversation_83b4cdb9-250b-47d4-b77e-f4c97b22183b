import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
    Alert,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    useColorScheme,
    View,
} from 'react-native';
import LiveChat from '../../components/shared/LiveChat';
import Header from '../../components/ui/Header';
import Sidebar from '../../components/ui/Sidebar';
import { BORDER_RADIUS, COLORS, FONTS, SHADOWS, SPACING } from '../../constants/theme';

export default function Support() {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';
  const [showChat, setShowChat] = useState(false);
  const [isSidebarVisible, setIsSidebarVisible] = useState(false);

  const handleCloseSidebar = () => {
    setIsSidebarVisible(false);
  };

  const handleContactMethod = (method: string) => {
    switch (method) {
      case 'phone':
        Alert.alert(
          'Call Support',
          'Would you like to call customer support?',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Call', onPress: () => console.log('Calling support...') }
          ]
        );
        break;
      case 'email':
        Alert.alert(
          'Email Support',
          'Would you like to email customer support?',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Email', onPress: () => console.log('Emailing support...') }
          ]
        );
        break;
      case 'chat':
        setShowChat(true);
        break;
      default:
        break;
    }
  };

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
    ]}>
      <Sidebar
        isVisible={isSidebarVisible}
        onClose={handleCloseSidebar}
      />
      <Header
        title="Support"
        showGreeting={false}
        onMenuPress={() => setIsSidebarVisible(true)}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Text style={[
          styles.title,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          How can we help you?
        </Text>

        <Text style={[
          styles.subtitle,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          Choose a contact method below
        </Text>

        <View style={styles.contactMethodsContainer}>
          <TouchableOpacity
            style={[
              styles.contactMethod,
              { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
            ]}
            onPress={() => handleContactMethod('phone')}
          >
            <View style={[styles.iconContainer, { backgroundColor: COLORS.primary }]}>
              <Ionicons name="call" size={28} color="#FFFFFF" />
            </View>
            <Text style={[
              styles.contactMethodTitle,
              { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
            ]}>
              Call Us
            </Text>
            <Text style={[
              styles.contactMethodDescription,
              { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
            ]}>
              Speak directly with our support team
            </Text>
            <Text style={[
              styles.contactMethodDetail,
              { color: COLORS.primary }
            ]}>
              +************
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.contactMethod,
              { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
            ]}
            onPress={() => handleContactMethod('email')}
          >
            <View style={[styles.iconContainer, { backgroundColor: COLORS.secondary }]}>
              <Ionicons name="mail" size={28} color="#FFFFFF" />
            </View>
            <Text style={[
              styles.contactMethodTitle,
              { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
            ]}>
              Email Us
            </Text>
            <Text style={[
              styles.contactMethodDescription,
              { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
            ]}>
              Send us an email with your query
            </Text>
            <Text style={[
              styles.contactMethodDetail,
              { color: COLORS.secondary }
            ]}>
              <EMAIL>
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.contactMethod,
              { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
            ]}
            onPress={() => handleContactMethod('chat')}
          >
            <View style={[styles.iconContainer, { backgroundColor: COLORS.status.success }]}>
              <Ionicons name="chatbubble-ellipses" size={28} color="#FFFFFF" />
            </View>
            <Text style={[
              styles.contactMethodTitle,
              { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
            ]}>
              Live Chat
            </Text>
            <Text style={[
              styles.contactMethodDescription,
              { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
            ]}>
              Chat with our support agents in real-time
            </Text>
            <Text style={[
              styles.contactMethodDetail,
              { color: COLORS.status.success }
            ]}>
              Available 24/7
            </Text>
          </TouchableOpacity>
        </View>

        <View style={[
          styles.officeInfoContainer,
          { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
        ]}>
          <Text style={[
            styles.officeInfoTitle,
            { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
          ]}>
            Visit Our Office
          </Text>

          <View style={styles.officeInfoRow}>
            <Ionicons
              name="location-outline"
              size={20}
              color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
              style={styles.officeInfoIcon}
            />
            <Text style={[
              styles.officeInfoText,
              { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
            ]}>
              123 Main Street, Gaborone, Botswana
            </Text>
          </View>

          <View style={styles.officeInfoRow}>
            <Ionicons
              name="time-outline"
              size={20}
              color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
              style={styles.officeInfoIcon}
            />
            <Text style={[
              styles.officeInfoText,
              { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
            ]}>
              Monday - Friday: 8:00 AM - 5:00 PM
            </Text>
          </View>
        </View>
      </ScrollView>

      <LiveChat isVisible={showChat} onClose={() => setShowChat(false)} />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: SPACING.lg,
    paddingBottom: SPACING.xxl,
  },
  title: {
    fontSize: FONTS.sizes.xxl,
    fontFamily: FONTS.bold,
    marginBottom: SPACING.xs,
  },
  subtitle: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    marginBottom: SPACING.lg,
  },
  contactMethodsContainer: {
    marginBottom: SPACING.lg,
  },
  contactMethod: {
    padding: SPACING.lg,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.md,
    ...SHADOWS.light.small,
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  contactMethodTitle: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.bold,
    marginBottom: SPACING.xs,
  },
  contactMethodDescription: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    marginBottom: SPACING.sm,
  },
  contactMethodDetail: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.bold,
  },
  officeInfoContainer: {
    padding: SPACING.lg,
    borderRadius: BORDER_RADIUS.md,
    ...SHADOWS.light.small,
  },
  officeInfoTitle: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.bold,
    marginBottom: SPACING.md,
  },
  officeInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  officeInfoIcon: {
    marginRight: SPACING.sm,
  },
  officeInfoText: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    flex: 1,
  },
});
