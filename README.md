# Inerca Mobile App

This is a mobile application for Inerca, a refined insurance solutions provider. The app allows users to manage their insurance policies and claims.

## Features

- Authentication (Login, Registration, Forgot Password)
- Welcome/Onboarding screens
- Home screen with user greeting
- Light and dark theme support
- Responsive UI

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm or yarn
- Expo CLI

### Installation

1. Clone the repository
   ```bash
   git clone <repository-url>
   cd i-nerca
   ```

2. Install dependencies
   ```bash
   npm install
   # or
   yarn install
   ```

3. Install additional dependencies
   ```bash
   npm install expo-secure-store
   # or
   yarn add expo-secure-store
   ```

4. Start the development server
   ```bash
   npx expo start
   ```

5. Run on a device or emulator
   - Scan the QR code with the Expo Go app on your mobile device
   - Press 'a' to run on an Android emulator
   - Press 'i' to run on an iOS simulator

## Project Structure

```
i-nerca/
├── app/                  # Main application screens
│   ├── splash.tsx        # Splash screen
│   ├── welcome.tsx       # Welcome/Onboarding screens
│   ├── login.tsx         # Login screen
│   ├── register.tsx      # Registration screen
│   ├── forgot-password.tsx # Forgot password screen
│   ├── home.tsx          # Home screen
│   ├── _layout.tsx       # Root layout with navigation setup
│   └── index.tsx         # Entry point (redirects to splash)
├── assets/               # Static assets
│   └── images/           # Images including logo
├── components/           # Reusable components
│   └── ui/               # UI components
│       ├── Button.tsx    # Custom button component
│       └── Input.tsx     # Custom input component
├── constants/            # App constants
│   └── theme.ts          # Theme configuration (colors, fonts, etc.)
└── context/              # React context providers
    └── AuthContext.tsx   # Authentication context
```

## API Integration

The app integrates with the Inerca backend API at `https://inerca-backend.fly.dev/` for authentication and data management.

## Color Scheme

The app uses a color scheme based on the Inerca brand:
- Primary: #F05A28 (Orange/coral from logo)
- Secondary: #2D4B9A (Blue from logo)
- With light and dark theme support
