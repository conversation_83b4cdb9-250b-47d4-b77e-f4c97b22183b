import { Stack, usePathname } from "expo-router";
import { useEffect, useState } from "react";
import Header from "../../components/ui/Header";

export default function AuthLayout() {
  const pathname = usePathname();
  const [title, setTitle] = useState("");

  useEffect(() => {
    // Set the title based on the current route
    if (pathname.includes("/login")) {
      setTitle("Login");
    } else if (pathname.includes("/register")) {
      setTitle("Register");
    } else if (pathname.includes("/forgot-password")) {
      setTitle("Forgot Password");
    }
  }, [pathname]);

  return (
    <Stack
      screenOptions={{
        header: () => {
          // Don't show back button on the main login screen
          const showBackButton = !pathname.endsWith("/login");
          return <Header title={title} showBackButton={showBackButton} />;
        },
        contentStyle: { backgroundColor: "transparent" },
      }}
    />
  );
}
