import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
  <PERSON>ert,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  useColorScheme,
  View,
} from 'react-native';
import Header from '../../components/ui/Header';
import Sidebar from '../../components/ui/Sidebar';
import { BORDER_RADIUS, COLORS, FONTS, SHADOWS, SPACING } from '../../constants/theme';

interface Policy {
  id: string;
  type: string;
  policyNumber: string;
  status: 'active' | 'expired' | 'pending';
  premium: string;
  renewalDate: string;
  coverage: string;
}

const mockPolicies: Policy[] = [
  {
    id: 'POL-001',
    type: 'Auto Insurance',
    policyNumber: 'AUT-2023-45678',
    status: 'active',
    premium: 'P2,400/year',
    renewalDate: '2024-03-15',
    coverage: 'Comprehensive',
  },
  {
    id: 'POL-002',
    type: 'Home Insurance',
    policyNumber: 'HOM-2023-98765',
    status: 'active',
    premium: 'P1,800/year',
    renewalDate: '2024-05-22',
    coverage: 'Full Coverage',
  },
  {
    id: 'POL-003',
    type: 'Life Insurance',
    policyNumber: 'LIF-2023-12345',
    status: 'pending',
    premium: 'P3,600/year',
    renewalDate: '2024-01-10',
    coverage: 'Term Life',
  },
];

export default function Policies() {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';
  const [isSidebarVisible, setIsSidebarVisible] = useState(false);

  const handleCloseSidebar = () => {
    setIsSidebarVisible(false);
  };

  const getStatusColor = (status: Policy['status']) => {
    switch (status) {
      case 'active':
        return COLORS.status.success;
      case 'expired':
        return COLORS.status.error;
      case 'pending':
        return COLORS.status.warning;
      default:
        return COLORS.status.info;
    }
  };

  const getStatusText = (status: Policy['status']) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'expired':
        return 'Expired';
      case 'pending':
        return 'Pending';
      default:
        return 'Unknown';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const handlePolicyPress = (policy: Policy) => {
    Alert.alert(
      'Policy Details',
      `View details for policy #${policy.policyNumber}`,
      [{ text: 'OK' }]
    );
  };

  const handleNewPolicy = () => {
    Alert.alert(
      'New Policy',
      'This feature would allow you to apply for a new policy in a real app.',
      [{ text: 'OK' }]
    );
  };

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
    ]}>
      <Sidebar
        isVisible={isSidebarVisible}
        onClose={handleCloseSidebar}
      />
      <Header
        title="My Policies"
        showGreeting={false}
        onMenuPress={() => setIsSidebarVisible(true)}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.headerContainer}>
          <Text style={[
            styles.title,
            { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
          ]}>
            My Policies
          </Text>

          <TouchableOpacity
            style={[styles.newPolicyButton, { backgroundColor: COLORS.primary }]}
            onPress={handleNewPolicy}
          >
            <Ionicons name="add" size={20} color="#FFFFFF" />
            <Text style={styles.newPolicyButtonText}>New Policy</Text>
          </TouchableOpacity>
        </View>

        {mockPolicies.length > 0 ? (
          <View style={styles.policiesContainer}>
            {mockPolicies.map((policy) => (
              <TouchableOpacity
                key={policy.id}
                style={[
                  styles.policyItem,
                  { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
                ]}
                onPress={() => handlePolicyPress(policy)}
              >
                <View style={styles.policyHeader}>
                  <Text style={[
                    styles.policyType,
                    { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
                  ]}>
                    {policy.type}
                  </Text>

                  <View style={[
                    styles.statusBadge,
                    { backgroundColor: getStatusColor(policy.status) }
                  ]}>
                    <Text style={styles.statusText}>
                      {getStatusText(policy.status)}
                    </Text>
                  </View>
                </View>

                <View style={styles.policyDetails}>
                  <View style={styles.policyDetail}>
                    <Text style={[
                      styles.policyLabel,
                      { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
                    ]}>
                      Policy Number:
                    </Text>
                    <Text style={[
                      styles.policyValue,
                      { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
                    ]}>
                      {policy.policyNumber}
                    </Text>
                  </View>

                  <View style={styles.policyDetail}>
                    <Text style={[
                      styles.policyLabel,
                      { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
                    ]}>
                      Premium:
                    </Text>
                    <Text style={[
                      styles.policyValue,
                      { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
                    ]}>
                      {policy.premium}
                    </Text>
                  </View>

                  <View style={styles.policyDetail}>
                    <Text style={[
                      styles.policyLabel,
                      { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
                    ]}>
                      Renewal Date:
                    </Text>
                    <Text style={[
                      styles.policyValue,
                      { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
                    ]}>
                      {formatDate(policy.renewalDate)}
                    </Text>
                  </View>

                  <View style={styles.policyDetail}>
                    <Text style={[
                      styles.policyLabel,
                      { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
                    ]}>
                      Coverage:
                    </Text>
                    <Text style={[
                      styles.policyValue,
                      { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
                    ]}>
                      {policy.coverage}
                    </Text>
                  </View>
                </View>

                <View style={styles.policyFooter}>
                  <Ionicons
                    name="chevron-forward"
                    size={20}
                    color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
                  />
                </View>
              </TouchableOpacity>
            ))}
          </View>
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons
              name="shield-outline"
              size={64}
              color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
            />
            <Text style={[
              styles.emptyText,
              { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
            ]}>
              No policies found
            </Text>
            <Text style={[
              styles.emptySubtext,
              { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
            ]}>
              Apply for a new policy to see it here
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: SPACING.lg,
    paddingBottom: SPACING.xxl,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  title: {
    fontSize: FONTS.sizes.xl,
    fontFamily: FONTS.bold,
  },
  newPolicyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.sm,
  },
  newPolicyButtonText: {
    color: '#FFFFFF',
    fontFamily: FONTS.medium,
    fontSize: FONTS.sizes.sm,
    marginLeft: SPACING.xs,
  },
  policiesContainer: {
    marginBottom: SPACING.lg,
  },
  policyItem: {
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    ...SHADOWS.light.small,
  },
  policyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  policyType: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.bold,
  },
  statusBadge: {
    paddingVertical: SPACING.xs / 2,
    paddingHorizontal: SPACING.sm,
    borderRadius: BORDER_RADIUS.sm,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: FONTS.sizes.xs,
    fontFamily: FONTS.medium,
  },
  policyDetails: {
    marginBottom: SPACING.sm,
  },
  policyDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.xs / 2,
  },
  policyLabel: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.medium,
  },
  policyValue: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.regular,
  },
  policyFooter: {
    alignItems: 'flex-end',
    marginTop: SPACING.xs,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  emptyText: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.medium,
    marginTop: SPACING.md,
  },
  emptySubtext: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    marginTop: SPACING.xs,
  },
});
