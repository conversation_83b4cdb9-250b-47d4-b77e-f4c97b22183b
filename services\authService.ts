import { ENDPOINTS } from '../constants/config';
import { apiClient } from './api';
import { BaseUserData, BusinessUserData, IndividualUserData } from '../context/AuthContext';

/**
 * Authentication service for handling auth-related API calls
 */
export const authService = {
  /**
   * Login user
   * @param email User email
   * @param password User password
   * @returns Login response
   */
  async login(email: string, password: string) {
    return apiClient.post(ENDPOINTS.LOGIN, { email, password });
  },

  /**
   * Register a new user
   * @param userData User registration data
   * @returns Registration response
   */
  async register(userData: BusinessUserData | IndividualUserData) {
    return apiClient.post(ENDPOINTS.REGISTER, userData);
  },

  /**
   * Get current user profile
   * @param token JWT token
   * @returns User profile
   */
  async getUserProfile(token: string) {
    return apiClient.get(ENDPOINTS.USER_PROFILE, token);
  },

  /**
   * Request password reset
   * @param email User email
   * @returns Password reset response
   */
  async requestPasswordReset(email: string) {
    return apiClient.post(ENDPOINTS.RESET_PASSWORD, { email });
  },

  /**
   * Reset password with token
   * @param email User email
   * @param token Reset token
   * @param newPassword New password
   * @returns Password reset confirmation
   */
  async resetPassword(email: string, token: string, newPassword: string) {
    return apiClient.post(`${ENDPOINTS.RESET_PASSWORD}/confirm`, {
      email,
      token,
      password: newPassword,
    });
  },

  /**
   * Update user profile
   * @param token JWT token
   * @param userData Updated user data
   * @returns Updated profile
   */
  async updateProfile(token: string, userData: Partial<BaseUserData>) {
    return apiClient.put(`${ENDPOINTS.USER_PROFILE}`, userData, token);
  },
};
