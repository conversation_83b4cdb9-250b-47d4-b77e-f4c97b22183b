import { router } from 'expo-router';
import * as SecureStore from 'expo-secure-store';
import React, { createContext, useContext, useEffect, useState } from 'react';

// Define types for our context
interface User {
  id: string;
  email: string;
  username?: string;
  phone_number?: string;
  role: string;
  is_phone_number_verified: boolean;
  is_email_verified: boolean;
  created_at: string;
  updated_at: string;
  // Additional fields for business and individual users
  business_name?: string;
  business_location?: string;
  business_address?: string;
  occupation?: string;
  date_of_birth?: string;
  physical_address?: string;
  user_type?: 'business' | 'individual';
}

interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isFirstTimeUser: boolean;
}

interface AuthContextType extends AuthState {
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (userData: RegisterUserData) => Promise<void>;
  signOut: () => Promise<void>;
  forgotPassword: (email: string) => Promise<any>;
  resetPassword: (email: string, newPassword: string) => Promise<any>;
  setIsFirstTimeUser: (value: boolean) => void;
}

// Registration data types
export interface BaseUserData {
  email: string;
  password: string;
  phone_number: string;
  user_type: 'business' | 'individual';
}

export interface BusinessUserData extends BaseUserData {
  user_type: 'business';
  business_name: string;
  business_location: string;
  business_address: string;
  contact_person: string;
}

export interface IndividualUserData extends BaseUserData {
  user_type: 'individual';
  full_name: string;
  date_of_birth: string;
  physical_address: string;
  occupation: string;
}

export type RegisterUserData = BusinessUserData | IndividualUserData;

// Create the context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState<AuthState>({
    user: null,
    token: null,
    isLoading: true,
    isFirstTimeUser: false,
  });

  // Load token and user data from secure storage on app start
  useEffect(() => {
    const bootstrapAsync = async () => {
      try {
        const token = await SecureStore.getItemAsync('userToken');
        const userString = await SecureStore.getItemAsync('userData');
        const isFirstTime = await SecureStore.getItemAsync('isFirstTimeUser');

        if (token && userString) {
          const user = JSON.parse(userString);
          setState(prevState => ({
            ...prevState,
            token,
            user,
            isLoading: false,
            isFirstTimeUser: isFirstTime === 'true',
          }));
        } else {
          setState(prevState => ({
            ...prevState,
            isLoading: false,
            isFirstTimeUser: isFirstTime === 'true',
          }));
        }
      } catch (error) {
        console.error('Failed to load auth state:', error);
        setState(prevState => ({
          ...prevState,
          isLoading: false,
        }));
      }
    };

    bootstrapAsync();
  }, []);

  // Sign in function
  const signIn = async (email: string, password: string) => {
    try {
      // Log the sign in attempt
      console.log('Sign in attempt:', { email, password });

      // Create a mock token
      const mockToken = 'mock_token_' + Math.random().toString(36).substring(2);

      // Create mock user data
      const mockUserData: User = {
        id: 'user_' + Math.random().toString(36).substring(2),
        email: email,
        username: email.split('@')[0],
        phone_number: '',
        role: 'user',
        is_phone_number_verified: true,
        is_email_verified: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_type: 'individual',
      };

      // Log the successful sign in
      console.log('Sign in successful:', mockUserData);

      // Save to secure storage
      await SecureStore.setItemAsync('userToken', mockToken);
      await SecureStore.setItemAsync('userData', JSON.stringify(mockUserData));

      // Update state
      setState({
        ...state,
        token: mockToken,
        user: mockUserData,
      });

      // Navigate to home
      router.replace('/(dashboard)/home');
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  };

  // Sign up function
  const signUp = async (userData: RegisterUserData) => {
    try {
      // Extract common fields
      const { email, phone_number, user_type } = userData;

      // Log the registration data
      console.log('Registration data:', userData);

      // Create a mock token
      const mockToken = 'mock_token_' + Math.random().toString(36).substring(2);

      // Create mock user data based on user type
      let mockUser: User;

      if (user_type === 'business') {
        const businessData = userData as BusinessUserData;
        mockUser = {
          id: 'user_' + Math.random().toString(36).substring(2),
          email,
          username: businessData.contact_person,
          phone_number,
          role: 'user',
          is_phone_number_verified: true,
          is_email_verified: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user_type: 'business',
          business_name: businessData.business_name,
          business_location: businessData.business_location,
          business_address: businessData.business_address,
        };
      } else {
        const individualData = userData as IndividualUserData;
        mockUser = {
          id: 'user_' + Math.random().toString(36).substring(2),
          email,
          username: individualData.full_name,
          phone_number,
          role: 'user',
          is_phone_number_verified: true,
          is_email_verified: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user_type: 'individual',
          date_of_birth: individualData.date_of_birth,
          physical_address: individualData.physical_address,
          occupation: individualData.occupation,
        };
      }

      // Log the successful registration
      console.log('Registration successful:', mockUser);

      // Save to secure storage
      await SecureStore.setItemAsync('userToken', mockToken);
      await SecureStore.setItemAsync('userData', JSON.stringify(mockUser));

      // Update state
      setState(prevState => ({
        ...prevState,
        token: mockToken,
        user: mockUser,
        isFirstTimeUser: true,
      }));

      // Set as first time user
      await SecureStore.setItemAsync('isFirstTimeUser', 'true');

      // Navigate to home
      router.replace('/(dashboard)/home');
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  };

  // Sign out function
  const signOut = async () => {
    try {
      // Clear secure storage
      await SecureStore.deleteItemAsync('userToken');
      await SecureStore.deleteItemAsync('userData');

      // Update state
      setState({
        ...state,
        token: null,
        user: null,
      });

      // Navigate to login
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  };

  // Forgot password function
  const forgotPassword = async (email: string) => {
    try {
      // Log the password reset request
      console.log(`Password reset requested for ${email}`);

      // In a real app, this would send a reset email or code
      // For now, we'll just log it and assume success
      return { success: true, message: 'Password reset code sent to your email' };
    } catch (error) {
      console.error('Forgot password error:', error);
      throw error;
    }
  };

  // Reset password function
  const resetPassword = async (email: string, newPassword: string) => {
    try {
      // Log the password reset
      console.log(`Password reset for ${email} with new password: ${newPassword}`);

      // In a real app, this would update the password in the database
      // For now, we'll just log it and assume success

      // Navigate to login
      router.replace('/(auth)/login');

      return { success: true, message: 'Password reset successful' };
    } catch (error) {
      console.error('Reset password error:', error);
      throw error;
    }
  };

  // Set first time user flag
  const setIsFirstTimeUser = (value: boolean) => {
    SecureStore.setItemAsync('isFirstTimeUser', value ? 'true' : 'false');
    setState({
      ...state,
      isFirstTimeUser: value,
    });
  };

  return (
    <AuthContext.Provider
      value={{
        ...state,
        signIn,
        signUp,
        signOut,
        forgotPassword,
        resetPassword,
        setIsFirstTimeUser,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
