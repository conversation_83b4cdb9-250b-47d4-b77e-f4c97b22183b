import React from 'react';
import { 
  TouchableOpacity, 
  Text, 
  StyleSheet, 
  ActivityIndicator,
  TouchableOpacityProps,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../../constants/theme';
import { useColorScheme } from 'react-native';

interface ButtonProps extends TouchableOpacityProps {
  title: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'text';
  size?: 'small' | 'medium' | 'large';
  isLoading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const Button: React.FC<ButtonProps> = ({
  title,
  variant = 'primary',
  size = 'medium',
  isLoading = false,
  style,
  textStyle,
  disabled,
  ...props
}) => {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  // Determine button styles based on variant and size
  const getButtonStyles = (): ViewStyle => {
    let baseStyle: ViewStyle = {
      borderRadius: BORDER_RADIUS.md,
      alignItems: 'center',
      justifyContent: 'center',
    };

    // Size styles
    switch (size) {
      case 'small':
        baseStyle = {
          ...baseStyle,
          paddingVertical: SPACING.xs,
          paddingHorizontal: SPACING.md,
        };
        break;
      case 'large':
        baseStyle = {
          ...baseStyle,
          paddingVertical: SPACING.md,
          paddingHorizontal: SPACING.xl,
        };
        break;
      default: // medium
        baseStyle = {
          ...baseStyle,
          paddingVertical: SPACING.sm,
          paddingHorizontal: SPACING.lg,
        };
    }

    // Variant styles
    switch (variant) {
      case 'secondary':
        baseStyle = {
          ...baseStyle,
          backgroundColor: COLORS.secondary,
        };
        break;
      case 'outline':
        baseStyle = {
          ...baseStyle,
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: isDark ? COLORS.ui.dark.border : COLORS.ui.light.border,
        };
        break;
      case 'text':
        baseStyle = {
          ...baseStyle,
          backgroundColor: 'transparent',
        };
        break;
      default: // primary
        baseStyle = {
          ...baseStyle,
          backgroundColor: COLORS.primary,
        };
    }

    // Disabled state
    if (disabled) {
      baseStyle = {
        ...baseStyle,
        opacity: 0.6,
      };
    }

    return baseStyle;
  };

  // Determine text styles based on variant and size
  const getTextStyles = (): TextStyle => {
    let baseStyle: TextStyle = {
      fontFamily: FONTS.medium,
    };

    // Size styles
    switch (size) {
      case 'small':
        baseStyle = {
          ...baseStyle,
          fontSize: FONTS.sizes.sm,
        };
        break;
      case 'large':
        baseStyle = {
          ...baseStyle,
          fontSize: FONTS.sizes.lg,
        };
        break;
      default: // medium
        baseStyle = {
          ...baseStyle,
          fontSize: FONTS.sizes.md,
        };
    }

    // Variant styles
    switch (variant) {
      case 'outline':
      case 'text':
        baseStyle = {
          ...baseStyle,
          color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary,
        };
        break;
      default: // primary, secondary
        baseStyle = {
          ...baseStyle,
          color: '#FFFFFF',
        };
    }

    return baseStyle;
  };

  return (
    <TouchableOpacity
      style={[getButtonStyles(), style]}
      disabled={disabled || isLoading}
      {...props}
    >
      {isLoading ? (
        <ActivityIndicator 
          size="small" 
          color={variant === 'outline' || variant === 'text' 
            ? (isDark ? COLORS.text.dark.primary : COLORS.text.light.primary)
            : '#FFFFFF'
          } 
        />
      ) : (
        <Text style={[getTextStyles(), textStyle]}>{title}</Text>
      )}
    </TouchableOpacity>
  );
};

export default Button;
