import React from 'react';
import {
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    useColorScheme
} from 'react-native';
import { COLORS, FONTS, SPACING } from '../../constants/theme';

export default function TermsAndConditions() {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
    ]}>
      <Header
        title="Terms & Conditions"
        showGreeting={false}
        showNotification={false}
        showMenu={false}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Text style={[
          styles.title,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          Terms and Conditions
        </Text>

        <Text style={[
          styles.date,
          { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
        ]}>
          Last Updated: June 1, 2023
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          Please read these Terms and Conditions ("Terms", "Terms and Conditions") carefully before using the Inerca mobile application (the "Service") operated by Inerca Insurance ("us", "we", or "our").
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          Your access to and use of the Service is conditioned on your acceptance of and compliance with these Terms. These Terms apply to all visitors, users, and others who access or use the Service.
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          By accessing or using the Service, you agree to be bound by these Terms. If you disagree with any part of the terms, then you may not access the Service.
        </Text>

        <Text style={[
          styles.sectionTitle,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          1. Accounts
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          When you create an account with us, you must provide accurate, complete, and up-to-date information at all times. Failure to do so constitutes a breach of the Terms, which may result in immediate termination of your account on our Service.
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          You are responsible for safeguarding the password that you use to access the Service and for any activities or actions under your password, whether your password is with our Service or a third-party service.
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          You agree not to disclose your password to any third party. You must notify us immediately upon becoming aware of any breach of security or unauthorized use of your account.
        </Text>

        <Text style={[
          styles.sectionTitle,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          2. Insurance Policies
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          The insurance policies displayed and managed through the Service are subject to the specific terms and conditions of each policy. The Service provides a platform for managing these policies, but the actual insurance coverage is governed by the policy documents issued by Inerca Insurance.
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          Any quotes provided through the Service are estimates and are subject to verification and approval by our underwriting team. Final premium amounts may differ based on additional information collected during the application process.
        </Text>

        <Text style={[
          styles.sectionTitle,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          3. Claims
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          Claims submitted through the Service are subject to verification and investigation. Providing false or misleading information in a claim may result in denial of the claim and possible legal action.
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          The Service provides a convenient way to submit claims, but the processing and settlement of claims are governed by the terms of your insurance policy.
        </Text>

        <Text style={[
          styles.sectionTitle,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          4. Termination
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms.
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          Upon termination, your right to use the Service will immediately cease. If you wish to terminate your account, you may simply discontinue using the Service.
        </Text>

        <Text style={[
          styles.sectionTitle,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          5. Changes
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days' notice prior to any new terms taking effect. What constitutes a material change will be determined at our sole discretion.
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          By continuing to access or use our Service after those revisions become effective, you agree to be bound by the revised terms. If you do not agree to the new terms, please stop using the Service.
        </Text>

        <Text style={[
          styles.sectionTitle,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          6. Contact Us
        </Text>

        <Text style={[
          styles.paragraph,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          If you have any questions about these Terms, please contact <NAME_EMAIL>.
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: SPACING.lg,
    paddingBottom: SPACING.xxl,
  },
  title: {
    fontSize: FONTS.sizes.xxl,
    fontFamily: FONTS.bold,
    marginBottom: SPACING.sm,
  },
  date: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.regular,
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.bold,
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
  },
  paragraph: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    marginBottom: SPACING.md,
    lineHeight: 24,
  },
});
