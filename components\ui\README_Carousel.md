# Carousel Component

A flexible and customizable carousel component for displaying promotional banners and policy cards in the Inerca mobile app.

## Features

- Supports two display types: promotional banners and policy cards
- Auto-play functionality with customizable interval
- Pagination indicators
- Touch-enabled navigation
- Responsive to screen width
- Supports dark and light themes
- Customizable gap between items

## Usage

### Basic Usage

```tsx
import Carousel, { CarouselItem } from '../components/ui/Carousel';

// Sample data
const promotionalBanners: CarouselItem[] = [
  {
    id: '1',
    title: 'Special Offer!',
    description: 'Get 15% off on new policies this month',
    backgroundColor: '#F05A28', // Primary color
    icon: 'arrow-forward-circle',
    image: require('../assets/images/logo.png'),
  },
  // Add more items...
];

// In your component
return (
  <Carousel
    data={promotionalBanners}
    type="banner"
    onItemPress={(item) => console.log('Banner pressed:', item.title)}
    autoPlay={true}
    autoPlayInterval={5000}
    showPagination={true}
  />
);
```

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `data` | `CarouselItem[]` | Required | Array of items to display in the carousel |
| `gap` | `number` | `16` | Gap between carousel items in pixels |
| `autoPlay` | `boolean` | `false` | Whether to auto-play the carousel |
| `autoPlayInterval` | `number` | `3000` | Interval between slides in milliseconds when auto-play is enabled |
| `showPagination` | `boolean` | `true` | Whether to show pagination dots |
| `onItemPress` | `(item: CarouselItem) => void` | - | Callback function when an item is pressed |
| `type` | `'banner' \| 'policy'` | `'banner'` | Type of carousel to display |

### CarouselItem Interface

```tsx
interface CarouselItem {
  id: string;
  title: string;
  description?: string;
  image?: any; // Image source
  backgroundColor?: string;
  icon?: string; // Ionicons name
  actionText?: string;
  policyNumber?: string; // For policy type
  expiryDate?: string; // For policy type
  coverageType?: string; // For policy type
}
```

## Examples

### Promotional Banners

```tsx
const promotionalBanners: CarouselItem[] = [
  {
    id: '1',
    title: 'Special Offer!',
    description: 'Get 15% off on new policies this month',
    backgroundColor: COLORS.primary,
    icon: 'arrow-forward-circle',
    image: require('../assets/images/logo.png'),
  },
  // Add more items...
];

<Carousel
  data={promotionalBanners}
  type="banner"
  onItemPress={handleBannerPress}
  autoPlay={true}
  autoPlayInterval={5000}
  showPagination={true}
/>
```

### Policy Cards

```tsx
const policyCards: CarouselItem[] = [
  {
    id: '1',
    title: 'Auto Insurance',
    policyNumber: 'AUT-2023-45678',
    coverageType: 'Comprehensive',
    expiryDate: 'Dec 31, 2023',
    icon: 'car-outline',
    backgroundColor: COLORS.primary,
    actionText: 'View Details',
  },
  // Add more items...
];

<Carousel
  data={policyCards}
  type="policy"
  onItemPress={handlePolicyPress}
  autoPlay={false}
  showPagination={true}
/>
```

## Integration with Home Screen

To integrate this carousel with your home screen, you can use the example in `components/examples/CarouselExample.tsx` as a reference.

## Customization

You can customize the appearance of the carousel by modifying the styles in the `Carousel.tsx` file. The component uses the theme colors from `constants/theme.ts` to ensure consistency with the rest of the app.
