import { Stack, usePathname } from "expo-router";
import { useEffect, useState } from "react";

export default function OnboardingLayout() {
  const pathname = usePathname();
  const [title, setTitle] = useState("");

  useEffect(() => {
    // Set the title based on the current route
    if (pathname.includes("/welcome")) {
      setTitle("Welcome");
    } else if (pathname.includes("/splash")) {
      setTitle(""); // No title for splash screen
    }
  }, [pathname]);

  return (
    <Stack
      screenOptions={{
        header: () => {
          // Only show header on welcome screen, not on splash
          if (pathname.includes("/splash")) {
            return null;
          }

          // Don't show back button on welcome screen
      
        },
        contentStyle: { backgroundColor: "transparent" },
        gestureEnabled: false,
      }}
    />
  );
}
