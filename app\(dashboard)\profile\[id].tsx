import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect } from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  useColorScheme,
  View
} from 'react-native';
import BusinessProfileForm from '../../../components/profile/BusinessProfileForm';
import CommonProfileForm from '../../../components/profile/CommonProfileForm';
import IndividualProfileForm from '../../../components/profile/IndividualProfileForm';
import Button from '../../../components/ui/Button';
import Header from '../../../components/ui/Header';
import { COLORS, FONTS, SPACING } from '../../../constants/theme';
import { useAuth } from '../../../context/AuthContext';
import { useProfileForm } from '../../../hooks/useProfileForm';

export default function ProfileDetails() {
  const { id } = useLocalSearchParams();
  const { user } = useAuth();
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  const isEditing = id === 'edit';
  const isCurrentUser = id === user?.id || id === 'edit';

  // Use custom hook for form handling
  const {
    formData,
    updateField,
    errors,
    isLoading,
    handleSave
  } = useProfileForm(user, id);

  // Redirect if trying to access another user's profile
  useEffect(() => {
    if (!isCurrentUser) {
      Alert.alert(
        'Access Denied',
        'You do not have permission to view this profile.',
        [{ text: 'OK', onPress: () => router.back() }]
      );
    }
  }, [isCurrentUser]);

  if (!isCurrentUser) {
    return null; // Will redirect in useEffect
  }

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
    ]}>
      <Header
        title={isEditing ? "Edit Profile" : "Profile Details"}
        showGreeting={false}
        showNotification={false}
        showMenu={false}
      />

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <Text style={[
            styles.sectionTitle,
            { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
          ]}>
            Personal Information
          </Text>

          {/* Common profile fields */}
          <CommonProfileForm
            formData={formData}
            errors={errors}
            updateField={updateField}
            isEditing={isEditing}
          />

          {/* User type specific fields */}
          {user?.user_type === 'individual' && (
            <IndividualProfileForm
              formData={formData}
              errors={errors}
              updateField={updateField}
              isEditing={isEditing}
            />
          )}

          {user?.user_type === 'business' && (
            <BusinessProfileForm
              formData={formData}
              errors={errors}
              updateField={updateField}
              isEditing={isEditing}
            />
          )}

          {isEditing && (
            <View style={styles.buttonContainer}>
              <Button
                title="Save Changes"
                onPress={handleSave}
                isLoading={isLoading}
                style={styles.saveButton}
              />

              <Button
                title="Cancel"
                variant="outline"
                onPress={() => router.back()}
                style={styles.cancelButton}
              />
            </View>
          )}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  // Layout
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: SPACING.lg,
    paddingBottom: SPACING.xxl,
  },

  // Typography
  sectionTitle: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.bold,
    marginBottom: SPACING.md,
  },

  // Buttons
  buttonContainer: {
    marginTop: SPACING.lg,
  },
  saveButton: {
    marginBottom: SPACING.md,
  },
  cancelButton: {},
});
