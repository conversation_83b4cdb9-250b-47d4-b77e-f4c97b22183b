// Theme configuration based on the Inerca brand colors from the image
// Primary colors from the Inerca logo (orange/coral and blue)

export const COLORS = {
  // Primary colors
  primary: '#F05A28', // Orange/coral from logo
  secondary: '#2D4B9A', // Blue from logo
  
  // Background colors
  background: {
    light: '#FFFFFF',
    dark: '#121212',
  },
  
  // Text colors
  text: {
    light: {
      primary: '#333333',
      secondary: '#666666',
      tertiary: '#999999',
    },
    dark: {
      primary: '#F5F5F5',
      secondary: '#B3B3B3',
      tertiary: '#737373',
    },
  },
  
  // UI colors
  ui: {
    light: {
      card: '#FFFFFF',
      input: '#F5F5F5',
      border: '#E0E0E0',
      disabled: '#CCCCCC',
    },
    dark: {
      card: '#1E1E1E',
      input: '#2A2A2A',
      border: '#3A3A3A',
      disabled: '#444444',
    },
  },
  
  // Status colors
  status: {
    success: '#4CAF50',
    warning: '#FFC107',
    error: '#F44336',
    info: '#2196F3',
  },
};

export const FONTS = {
  regular: 'System',
  medium: 'System-Medium',
  bold: 'System-Bold',
  
  sizes: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 30,
  },
};

export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const SHADOWS = {
  light: {
    small: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.2,
      shadowRadius: 1.41,
      elevation: 2,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.23,
      shadowRadius: 2.62,
      elevation: 4,
    },
    large: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.3,
      shadowRadius: 4.65,
      elevation: 8,
    },
  },
  dark: {
    small: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.4,
      shadowRadius: 1.41,
      elevation: 2,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.43,
      shadowRadius: 2.62,
      elevation: 4,
    },
    large: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.5,
      shadowRadius: 4.65,
      elevation: 8,
    },
  },
};

export const BORDER_RADIUS = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 24,
  xxl: 32,
  round: 9999,
};
