import { Stack, router } from 'expo-router';
import React, { useEffect } from 'react';
import { useColorScheme } from 'react-native';
import LiveChat from '../../components/shared/LiveChat';
import { COLORS } from '../../constants/theme';
import { useAuth } from '../../context/AuthContext';

export default function DashboardLayout() {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';
  const { user, isLoading } = useAuth();

  // Check if user is authenticated
  useEffect(() => {
    if (!isLoading && !user) {
      // Redirect to login if not authenticated
      router.replace('/(auth)/login');
    }
  }, [user, isLoading]);

  // Don't render anything while checking authentication
  if (isLoading) {
    return null;
  }

  return (
    <>
      <Stack
        screenOptions={{
          headerShown: false,
          contentStyle: {
            backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light,
          },
        }}
      >
        <Stack.Screen name="home" />
        <Stack.Screen name="profile" />
        <Stack.Screen name="profile/[id]" />
        <Stack.Screen name="quote" />
        <Stack.Screen name="policies" />
        <Stack.Screen name="more" />
        <Stack.Screen name="settings" />
        <Stack.Screen
          name="support"
          listeners={{
            focus: () => {
              // Redirect to the support page in the support group
              router.replace('/(support)/support');
            },
          }}
        />
        <Stack.Screen
          name="faq"
          listeners={{
            focus: () => {
              // Redirect to the FAQ page in the support group
              router.replace('/(support)/faq');
            },
          }}
        />
        <Stack.Screen
          name="terms"
          listeners={{
            focus: () => {
              // Redirect to the terms page in the legal group
              router.replace('/(legal)/terms');
            },
          }}
        />
        <Stack.Screen
          name="privacy"
          listeners={{
            focus: () => {
              // Redirect to the privacy page in the legal group
              router.replace('/(legal)/privacy');
            },
          }}
        />
        <Stack.Screen name="documents" />
        <Stack.Screen name="claims" />
        <Stack.Screen name="notifications" />
      </Stack>

      {/* Live Chat component that appears on all screens */}
      <LiveChat />
    </>
  );
}
