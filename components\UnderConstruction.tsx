import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React from 'react';
import {
    StyleSheet,
    Text,
    TouchableOpacity,
    useColorScheme,
    View,
} from 'react-native';
import { COLORS, FONTS, SPACING } from '../constants/theme';

interface UnderConstructionProps {
  title?: string;
  message?: string;
  showBackButton?: boolean;
  onBackPress?: () => void;
}

const UnderConstruction: React.FC<UnderConstructionProps> = ({
  title = 'Under Construction',
  message = 'This feature is currently under development and will be available soon.',
  showBackButton = true,
  onBackPress,
}) => {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      router.back();
    }
  };

  return (
    <View style={[
      styles.container,
      { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
    ]}>
      <Ionicons
        name="construct"
        size={80}
        color={COLORS.primary}
        style={styles.icon}
      />
      
      <Text style={[
        styles.title,
        { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
      ]}>
        {title}
      </Text>
      
      <Text style={[
        styles.message,
        { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
      ]}>
        {message}
      </Text>
      
      {showBackButton && (
        <TouchableOpacity
          style={[
            styles.backButton,
            { backgroundColor: COLORS.primary }
          ]}
          onPress={handleBackPress}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
  },
  icon: {
    marginBottom: SPACING.lg,
  },
  title: {
    fontSize: FONTS.sizes.xxl,
    fontFamily: FONTS.bold,
    textAlign: 'center',
    marginBottom: SPACING.md,
  },
  message: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    textAlign: 'center',
    marginBottom: SPACING.xl,
    lineHeight: 24,
  },
  backButton: {
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.lg,
    borderRadius: 8,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.medium,
  },
});

export default UnderConstruction;
