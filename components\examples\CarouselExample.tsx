import React from 'react';
import { View, StyleSheet, Text, useColorScheme, SafeAreaView, ScrollView } from 'react-native';
import Carousel, { CarouselItem } from '../ui/Carousel';
import { COLORS, FONTS, SPACING } from '../../constants/theme';

// Sample promotional banners data
const promotionalBanners: CarouselItem[] = [
  {
    id: '1',
    title: 'Special Offer!',
    description: 'Get 15% off on new policies this month',
    backgroundColor: COLORS.primary,
    icon: 'arrow-forward-circle',
    image: require('../../assets/images/logo.png'),
  },
  {
    id: '2',
    title: 'Bundle & Save',
    description: 'Save up to 20% when you bundle home & auto',
    backgroundColor: COLORS.secondary,
    icon: 'arrow-forward-circle',
    image: require('../../assets/images/logo.png'),
  },
  {
    id: '3',
    title: 'New Mobile App',
    description: 'Manage your policies on the go with our new app',
    backgroundColor: '#4A90E2',
    icon: 'arrow-forward-circle',
    image: require('../../assets/images/logo.png'),
  },
];

// Sample policy cards data
const policyCards: CarouselItem[] = [
  {
    id: '1',
    title: 'Auto Insurance',
    policyNumber: 'AUT-2023-45678',
    coverageType: 'Comprehensive',
    expiryDate: 'Dec 31, 2023',
    icon: 'car-outline',
    backgroundColor: COLORS.primary,
    actionText: 'View Details',
  },
  {
    id: '2',
    title: 'Home Insurance',
    policyNumber: 'HOM-2023-98765',
    coverageType: 'Standard',
    expiryDate: 'Nov 15, 2023',
    icon: 'home-outline',
    backgroundColor: COLORS.secondary,
    actionText: 'View Details',
  },
  {
    id: '3',
    title: 'Life Insurance',
    policyNumber: 'LIF-2023-12345',
    coverageType: 'Term Life',
    expiryDate: 'Jan 20, 2024',
    icon: 'heart-outline',
    backgroundColor: '#4A90E2',
    actionText: 'View Details',
  },
];

const CarouselExample: React.FC = () => {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  const handleBannerPress = (item: CarouselItem) => {
    console.log('Banner pressed:', item.title);
  };

  const handlePolicyPress = (item: CarouselItem) => {
    console.log('Policy pressed:', item.title);
  };

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
    ]}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Text style={[
          styles.sectionTitle,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          Your Policies
        </Text>

        <Carousel
          data={policyCards}
          type="policy"
          onItemPress={handlePolicyPress}
          autoPlay={false}
          showPagination={true}
        />

        <Text style={[
          styles.sectionTitle,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          Special Offers
        </Text>

        <Carousel
          data={promotionalBanners}
          type="banner"
          onItemPress={handleBannerPress}
          autoPlay={true}
          autoPlayInterval={5000}
          showPagination={true}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingVertical: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.bold,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
    marginTop: SPACING.lg,
  },
});

export default CarouselExample;
