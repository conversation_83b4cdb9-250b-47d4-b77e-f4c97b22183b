import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
  Alert,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  useColorScheme,
  View,
} from 'react-native';
import Header from '../../components/ui/Header';
import Sidebar from '../../components/ui/Sidebar';
import { BORDER_RADIUS, COLORS, FONTS, SHADOWS, SPACING } from '../../constants/theme';

interface Document {
  id: string;
  name: string;
  type: string;
  date: string;
  size: string;
  icon: string;
}

const mockDocuments: Document[] = [
  {
    id: 'DOC-001',
    name: 'Auto Insurance Policy',
    type: 'PDF',
    date: '2023-10-15',
    size: '1.2 MB',
    icon: 'document-text-outline',
  },
  {
    id: 'DOC-002',
    name: 'Home Insurance Certificate',
    type: 'PDF',
    date: '2023-09-22',
    size: '0.8 MB',
    icon: 'document-text-outline',
  },
  {
    id: 'DOC-003',
    name: 'Claim Receipt',
    type: 'Image',
    date: '2023-11-05',
    size: '2.4 MB',
    icon: 'image-outline',
  },
];

export default function Documents() {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';
  const [isSidebarVisible, setIsSidebarVisible] = useState(false);

  const handleCloseSidebar = () => {
    setIsSidebarVisible(false);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const handleDocumentPress = (document: Document) => {
    Alert.alert(
      'Document Action',
      `What would you like to do with ${document.name}?`,
      [
        { text: 'View', onPress: () => console.log(`Viewing ${document.name}`) },
        { text: 'Share', onPress: () => console.log(`Sharing ${document.name}`) },
        { text: 'Download', onPress: () => console.log(`Downloading ${document.name}`) },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleUpload = () => {
    Alert.alert(
      'Upload Document',
      'This feature would allow you to upload documents in a real app.',
      [{ text: 'OK' }]
    );
  };

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
    ]}>
      <Sidebar
        isVisible={isSidebarVisible}
        onClose={handleCloseSidebar}
      />
      <Header
        title="Documents"
        showGreeting={false}
        onMenuPress={() => setIsSidebarVisible(true)}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.headerContainer}>
          <Text style={[
            styles.title,
            { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
          ]}>
            My Documents
          </Text>

          <TouchableOpacity
            style={[styles.uploadButton, { backgroundColor: COLORS.primary }]}
            onPress={handleUpload}
          >
            <Ionicons name="cloud-upload-outline" size={20} color="#FFFFFF" />
            <Text style={styles.uploadButtonText}>Upload</Text>
          </TouchableOpacity>
        </View>

        {mockDocuments.length > 0 ? (
          <View style={styles.documentsContainer}>
            {mockDocuments.map((document) => (
              <TouchableOpacity
                key={document.id}
                style={[
                  styles.documentItem,
                  { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
                ]}
                onPress={() => handleDocumentPress(document)}
              >
                <View style={[
                  styles.documentIconContainer,
                  { backgroundColor: document.type === 'PDF' ? COLORS.primary : COLORS.secondary }
                ]}>
                  <Ionicons name={document.icon as any} size={24} color="#FFFFFF" />
                </View>

                <View style={styles.documentInfo}>
                  <Text style={[
                    styles.documentName,
                    { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
                  ]}>
                    {document.name}
                  </Text>

                  <View style={styles.documentDetails}>
                    <Text style={[
                      styles.documentDetail,
                      { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
                    ]}>
                      {document.type} • {document.size}
                    </Text>
                    <Text style={[
                      styles.documentDetail,
                      { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
                    ]}>
                      {formatDate(document.date)}
                    </Text>
                  </View>
                </View>

                <TouchableOpacity
                  style={styles.documentAction}
                  onPress={() => handleDocumentPress(document)}
                >
                  <Ionicons
                    name="ellipsis-vertical"
                    size={20}
                    color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
                  />
                </TouchableOpacity>
              </TouchableOpacity>
            ))}
          </View>
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons
              name="document-outline"
              size={64}
              color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
            />
            <Text style={[
              styles.emptyText,
              { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
            ]}>
              No documents found
            </Text>
            <Text style={[
              styles.emptySubtext,
              { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
            ]}>
              Upload documents to see them here
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: SPACING.lg,
    paddingBottom: SPACING.xxl,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  title: {
    fontSize: FONTS.sizes.xl,
    fontFamily: FONTS.bold,
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.sm,
  },
  uploadButtonText: {
    color: '#FFFFFF',
    fontFamily: FONTS.medium,
    fontSize: FONTS.sizes.sm,
    marginLeft: SPACING.xs,
  },
  documentsContainer: {
    marginBottom: SPACING.lg,
  },
  documentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    ...SHADOWS.light.small,
  },
  documentIconContainer: {
    width: 48,
    height: 48,
    borderRadius: BORDER_RADIUS.sm,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  documentInfo: {
    flex: 1,
  },
  documentName: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.medium,
    marginBottom: SPACING.xs,
  },
  documentDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  documentDetail: {
    fontSize: FONTS.sizes.xs,
    fontFamily: FONTS.regular,
  },
  documentAction: {
    padding: SPACING.sm,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  emptyText: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.medium,
    marginTop: SPACING.md,
  },
  emptySubtext: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    marginTop: SPACING.xs,
  },
});
