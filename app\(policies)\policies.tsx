import React from 'react';
import { SafeAreaView, StyleSheet, useColorScheme } from 'react-native';
import Header from '../../components/ui/Header';
import UnderConstruction from '../../components/UnderConstruction';
import { COLORS } from '../../constants/theme';

export default function Policies() {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
    ]}>
      <Header
        title="My Policies"
        showBackButton={false}
        showGreeting={false}
      />

      <UnderConstruction
        title="My Policies"
        message="The policies feature is coming soon. You'll be able to view and manage all your insurance policies here."
        showBackButton={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
