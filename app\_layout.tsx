import { Stack } from "expo-router";
import * as SecureStore from "expo-secure-store";
import { StatusBar } from "expo-status-bar";
import { useEffect } from "react";
import { AuthProvider } from "../context/AuthContext";

// Ensure we have the SecureStore module
const ensureSecureStore = async () => {
  try {
    await SecureStore.getItemAsync("test");
  } catch (error) {
    console.error("SecureStore not available:", error);
  }
};

export default function RootLayout() {
  useEffect(() => {
    ensureSecureStore();
  }, []);

  return (
    <AuthProvider>
      <StatusBar style="auto" />
      <Stack
        screenOptions={{
          headerShown: false,
          contentStyle: { backgroundColor: "transparent" },
        }}
      >
        <Stack.Screen name="index" options={{ gestureEnabled: false }} />
        <Stack.Screen name="(auth)" options={{ gestureEnabled: false }} />
        <Stack.Screen name="(dashboard)" options={{ gestureEnabled: false }} />
        <Stack.Screen name="(onboarding)" options={{ gestureEnabled: false }} />
        <Stack.Screen name="(legal)" options={{ gestureEnabled: false }} />
        <Stack.Screen name="(support)" options={{ gestureEnabled: false }} />
      </Stack>
    </AuthProvider>
  );
}
