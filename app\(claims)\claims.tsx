import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import {
  <PERSON>ert,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  useColorScheme,
  View,
} from 'react-native';
import Header from '../../components/ui/Header';
import { BORDER_RADIUS, COLORS, FONTS, SHADOWS, SPACING } from '../../constants/theme';

interface Claim {
  id: string;
  policyNumber: string;
  type: string;
  date: string;
  amount: string;
  status: 'pending' | 'approved' | 'rejected' | 'in_review';
}

const mockClaims: Claim[] = [
  {
    id: '1',
    policyNumber: 'POL-123456',
    type: 'Auto Insurance',
    date: '2023-05-10',
    amount: 'P5,000.00',
    status: 'approved',
  },
  {
    id: '2',
    policyNumber: 'POL-789012',
    type: 'Home Insurance',
    date: '2023-04-15',
    amount: 'P12,500.00',
    status: 'pending',
  },
  {
    id: '3',
    policyNumber: 'POL-345678',
    type: 'Health Insurance',
    date: '2023-03-22',
    amount: 'P8,750.00',
    status: 'in_review',
  },
  {
    id: '4',
    policyNumber: 'POL-901234',
    type: 'Travel Insurance',
    date: '2023-02-05',
    amount: 'P3,200.00',
    status: 'rejected',
  },
];

export default function Claims() {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  const getStatusColor = (status: Claim['status']) => {
    switch (status) {
      case 'approved':
        return COLORS.status.success;
      case 'pending':
        return COLORS.status.warning;
      case 'in_review':
        return COLORS.primary;
      case 'rejected':
        return COLORS.status.error;
      default:
        return COLORS.text.light.tertiary;
    }
  };

  const getStatusText = (status: Claim['status']) => {
    switch (status) {
      case 'approved':
        return 'Approved';
      case 'pending':
        return 'Pending';
      case 'in_review':
        return 'In Review';
      case 'rejected':
        return 'Rejected';
      default:
        return 'Unknown';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const handleNewClaim = () => {
    Alert.alert(
      'New Claim',
      'This feature would allow you to file a new claim in a real app.',
      [{ text: 'OK' }]
    );
  };

  const handleClaimPress = (claim: Claim) => {
    Alert.alert(
      'Claim Details',
      `View details for claim #${claim.id}`,
      [{ text: 'OK' }]
    );
  };

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
    ]}>
      <Header
        title="Claims"
        showGreeting={false}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.headerContainer}>
          <Text style={[
            styles.title,
            { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
          ]}>
            My Claims
          </Text>

          <TouchableOpacity
            style={[styles.newClaimButton, { backgroundColor: COLORS.primary }]}
            onPress={handleNewClaim}
          >
            <Ionicons name="add" size={20} color="#FFFFFF" />
            <Text style={styles.newClaimButtonText}>New Claim</Text>
          </TouchableOpacity>
        </View>

        {mockClaims.length > 0 ? (
          <View style={styles.claimsContainer}>
            {mockClaims.map((claim) => (
              <TouchableOpacity
                key={claim.id}
                style={[
                  styles.claimItem,
                  { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
                ]}
                onPress={() => handleClaimPress(claim)}
              >
                <View style={styles.claimHeader}>
                  <Text style={[
                    styles.claimType,
                    { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
                  ]}>
                    {claim.type}
                  </Text>

                  <View style={[
                    styles.statusBadge,
                    { backgroundColor: getStatusColor(claim.status) }
                  ]}>
                    <Text style={styles.statusText}>
                      {getStatusText(claim.status)}
                    </Text>
                  </View>
                </View>

                <View style={styles.claimDetails}>
                  <View style={styles.claimDetail}>
                    <Text style={[
                      styles.claimDetailLabel,
                      { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
                    ]}>
                      Policy Number
                    </Text>
                    <Text style={[
                      styles.claimDetailValue,
                      { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
                    ]}>
                      {claim.policyNumber}
                    </Text>
                  </View>

                  <View style={styles.claimDetail}>
                    <Text style={[
                      styles.claimDetailLabel,
                      { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
                    ]}>
                      Date Filed
                    </Text>
                    <Text style={[
                      styles.claimDetailValue,
                      { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
                    ]}>
                      {formatDate(claim.date)}
                    </Text>
                  </View>

                  <View style={styles.claimDetail}>
                    <Text style={[
                      styles.claimDetailLabel,
                      { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
                    ]}>
                      Amount
                    </Text>
                    <Text style={[
                      styles.claimDetailValue,
                      { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
                    ]}>
                      {claim.amount}
                    </Text>
                  </View>
                </View>

                <View style={styles.claimFooter}>
                  <TouchableOpacity
                    style={[
                      styles.viewDetailsButton,
                      { borderColor: isDark ? COLORS.ui.dark.border : COLORS.ui.light.border }
                    ]}
                    onPress={() => handleClaimPress(claim)}
                  >
                    <Text style={[
                      styles.viewDetailsText,
                      { color: COLORS.primary }
                    ]}>
                      View Details
                    </Text>
                    <Ionicons name="chevron-forward" size={16} color={COLORS.primary} />
                  </TouchableOpacity>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons
              name="document-outline"
              size={64}
              color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
            />
            <Text style={[
              styles.emptyText,
              { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
            ]}>
              No claims found
            </Text>
            <Text style={[
              styles.emptySubtext,
              { color: isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary }
            ]}>
              File a new claim to see it here
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: SPACING.lg,
    paddingBottom: SPACING.xxl,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  title: {
    fontSize: FONTS.sizes.xl,
    fontFamily: FONTS.bold,
  },
  newClaimButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  newClaimButtonText: {
    color: '#FFFFFF',
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.medium,
    marginLeft: SPACING.xs,
  },
  claimsContainer: {
    marginBottom: SPACING.lg,
  },
  claimItem: {
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.md,
    ...SHADOWS.light.small,
  },
  claimHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  claimType: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.bold,
  },
  statusBadge: {
    paddingVertical: SPACING.xs / 2,
    paddingHorizontal: SPACING.sm,
    borderRadius: BORDER_RADIUS.round,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: FONTS.sizes.xs,
    fontFamily: FONTS.medium,
  },
  claimDetails: {
    marginBottom: SPACING.sm,
  },
  claimDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.xs,
  },
  claimDetailLabel: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.regular,
  },
  claimDetailValue: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.medium,
  },
  claimFooter: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.05)',
    paddingTop: SPACING.sm,
    alignItems: 'flex-end',
  },
  viewDetailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    borderRadius: BORDER_RADIUS.sm,
    borderWidth: 1,
  },
  viewDetailsText: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.medium,
    marginRight: SPACING.xs,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xxl,
  },
  emptyText: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.bold,
    marginTop: SPACING.md,
    marginBottom: SPACING.xs,
  },
  emptySubtext: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    textAlign: 'center',
  },
});
