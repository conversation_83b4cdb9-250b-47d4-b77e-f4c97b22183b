import { API_URL } from '../constants/config';

/**
 * Base API configuration
 */
const API_CONFIG = {
  baseUrl: API_URL,
  timeout: 10000, // 10 seconds
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
};

// Add timeout wrapper
const fetchWithTimeout = (url: string, options: RequestInit) => {
  return Promise.race([
    fetch(url, options),
    new Promise<Response>((_, reject) =>
      setTimeout(() => reject(new Error('Request timeout')), API_CONFIG.timeout)
    )
  ]);
};

/**
 * Error response from API
 */
export interface ApiError {
  message: string;
  status: number;
  errors?: Record<string, string[]>;
}

/**
 * Generic API response
 */
export interface ApiResponse<T> {
  data?: T;
  error?: ApiError;
}

/**
 * Add authorization header to request
 * @param token JWT token
 * @returns Headers with authorization
 */
const authHeaders = (token: string) => ({
  ...API_CONFIG.headers,
  'Authorization': `Bearer ${token}`,
});

/**
 * Handle API errors
 * @param response Fetch response
 * @returns Formatted error
 */
const handleApiError = async (response: Response): Promise<ApiError> => {
  try {
    const data = await response.json();
    return {
      message: data.message || 'An error occurred',
      status: response.status,
      errors: data.errors,
    };
  } catch (error) {
    return {
      message: 'Network error',
      status: response.status,
    };
  }
};

/**
 * Base API client with common methods
 */
export const apiClient = {
  /**
   * Make a GET request
   * @param endpoint API endpoint
   * @param token Optional JWT token
   * @returns API response
   */
  async get<T>(endpoint: string, token?: string): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${API_CONFIG.baseUrl}${endpoint}`, {
        method: 'GET',
        headers: token ? authHeaders(token) : API_CONFIG.headers,
      });

      if (!response.ok) {
        const error = await handleApiError(response);
        return { error };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          message: 'Network error',
          status: 0,
        },
      };
    }
  },

  /**
   * Make a POST request
   * @param endpoint API endpoint
   * @param body Request body
   * @param token Optional JWT token
   * @returns API response
   */
  async post<T>(endpoint: string, body: any, token?: string): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${API_CONFIG.baseUrl}${endpoint}`, {
        method: 'POST',
        headers: token ? authHeaders(token) : API_CONFIG.headers,
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const error = await handleApiError(response);
        return { error };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          message: 'Network error',
          status: 0,
        },
      };
    }
  },

  /**
   * Make a PUT request
   * @param endpoint API endpoint
   * @param body Request body
   * @param token JWT token
   * @returns API response
   */
  async put<T>(endpoint: string, body: any, token: string): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${API_CONFIG.baseUrl}${endpoint}`, {
        method: 'PUT',
        headers: authHeaders(token),
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const error = await handleApiError(response);
        return { error };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          message: 'Network error',
          status: 0,
        },
      };
    }
  },

  /**
   * Make a DELETE request
   * @param endpoint API endpoint
   * @param token JWT token
   * @returns API response
   */
  async delete<T>(endpoint: string, token: string): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${API_CONFIG.baseUrl}${endpoint}`, {
        method: 'DELETE',
        headers: authHeaders(token),
      });

      if (!response.ok) {
        const error = await handleApiError(response);
        return { error };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          message: 'Network error',
          status: 0,
        },
      };
    }
  },
};

