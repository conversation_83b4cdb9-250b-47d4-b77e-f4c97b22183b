import React from 'react';
import { Ionicons } from '@expo/vector-icons';
import { useColorScheme } from 'react-native';
import Input from '../ui/Input';
import DatePicker from '../ui/DatePicker';
import { COLORS } from '../../constants/theme';
import { ProfileFormData, ProfileFormErrors } from '../../hooks/useProfileForm';

interface IndividualProfileFormProps {
  formData: ProfileFormData;
  errors: ProfileFormErrors;
  updateField: (field: keyof ProfileFormData, value: string) => void;
  isEditing: boolean;
}

const IndividualProfileForm: React.FC<IndividualProfileFormProps> = ({
  formData,
  errors,
  updateField,
  isEditing,
}) => {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  return (
    <>
      <DatePicker
        label="Date of Birth"
        placeholder="Select date of birth"
        value={formData.dateOfBirth}
        onChange={(value) => updateField('dateOfBirth', value)}
        error={errors.dateOfBirth}
      />

      <Input
        label="Occupation"
        placeholder="Enter your occupation"
        value={formData.occupation}
        onChangeText={(value) => updateField('occupation', value)}
        error={errors.occupation}
        editable={isEditing}
        leftIcon={<Ionicons name="briefcase-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
      />

      <Input
        label="Physical Address"
        placeholder="Enter your address"
        value={formData.address}
        onChangeText={(value) => updateField('address', value)}
        error={errors.address}
        editable={isEditing}
        leftIcon={<Ionicons name="home-outline" size={20} color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary} />}
      />
    </>
  );
};

export default IndividualProfileForm;
