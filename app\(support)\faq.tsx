import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    useColorScheme,
    View,
} from 'react-native';
import Header from '../../components/ui/Header';
import Sidebar from '../../components/ui/Sidebar';
import { BORDER_RADIUS, COLORS, FONTS, SHADOWS, SPACING } from '../../constants/theme';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
}

const faqData: FAQItem[] = [
  {
    id: '1',
    question: 'How do I get a quote for insurance?',
    answer: 'You can get a quote by navigating to the "Get Quote" section in the app. Fill in the required details about what you want to insure, and we will provide you with a customized quote.',
    category: 'General',
  },
  {
    id: '2',
    question: 'How do I file a claim?',
    answer: 'To file a claim, go to the "Claims" section in the app. Provide details about the incident, upload any relevant photos or documents, and submit your claim. Our team will review it and get back to you promptly.',
    category: 'Claims',
  },
  {
    id: '3',
    question: 'What types of insurance do you offer?',
    answer: 'We offer a wide range of insurance products including auto, home, business, health, travel, and life insurance. Each can be customized to meet your specific needs.',
    category: 'Products',
  },
  {
    id: '4',
    question: 'How do I update my policy information?',
    answer: 'You can update your policy information by going to "My Policies" and selecting the policy you want to modify. Click on "Edit" to make changes to your coverage, personal details, or payment information.',
    category: 'Policies',
  },
  {
    id: '5',
    question: 'What payment methods do you accept?',
    answer: 'We accept various payment methods including credit/debit cards, bank transfers, and mobile money. You can set up automatic payments or pay manually through the app.',
    category: 'Billing',
  },
  {
    id: '6',
    question: 'How long does it take to process a claim?',
    answer: 'Most claims are processed within 3-5 business days after all required documentation has been submitted. Complex claims may take longer to review and process.',
    category: 'Claims',
  },
  {
    id: '7',
    question: 'Can I cancel my policy at any time?',
    answer: 'Yes, you can cancel your policy at any time. However, there may be cancellation fees depending on your policy terms. Please contact our support team for assistance with cancellations.',
    category: 'Policies',
  },
  {
    id: '8',
    question: 'How do I reset my password?',
    answer: 'To reset your password, go to the login screen and click on "Forgot Password". Follow the instructions sent to your registered email address to create a new password.',
    category: 'Account',
  },
];

const FAQItem: React.FC<{ item: FAQItem; isExpanded: boolean; onToggle: () => void }> = ({
  item,
  isExpanded,
  onToggle,
}) => {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  return (
    <TouchableOpacity
      style={[
        styles.faqItem,
        { backgroundColor: isDark ? COLORS.ui.dark.card : COLORS.ui.light.card }
      ]}
      onPress={onToggle}
    >
      <View style={styles.faqHeader}>
        <Text style={[
          styles.faqQuestion,
          { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
        ]}>
          {item.question}
        </Text>
        <Ionicons
          name={isExpanded ? 'chevron-up' : 'chevron-down'}
          size={20}
          color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
        />
      </View>

      {isExpanded && (
        <Text style={[
          styles.faqAnswer,
          { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
        ]}>
          {item.answer}
        </Text>
      )}

      <View style={styles.categoryContainer}>
        <Text style={[
          styles.categoryText,
          { color: COLORS.primary }
        ]}>
          {item.category}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

export default function FAQ() {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';
  const [expandedId, setExpandedId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSidebarVisible, setIsSidebarVisible] = useState(false);

  const handleCloseSidebar = () => {
    setIsSidebarVisible(false);
  };

  const toggleExpand = (id: string) => {
    setExpandedId(expandedId === id ? null : id);
  };

  const filteredFAQs = faqData.filter(item =>
    item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
    ]}>
      <Sidebar
        isVisible={isSidebarVisible}
        onClose={handleCloseSidebar}
      />
      <Header
        title="FAQ"
        showGreeting={false}
        onMenuPress={() => setIsSidebarVisible(true)}
      />

      <View style={styles.searchContainer}>
        <View style={[
          styles.searchInputContainer,
          { backgroundColor: isDark ? COLORS.ui.dark.input : COLORS.ui.light.input }
        ]}>
          <Ionicons
            name="search"
            size={20}
            color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
            style={styles.searchIcon}
          />
          <TextInput
            style={[
              styles.searchInput,
              { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
            ]}
            placeholder="Search FAQs..."
            placeholderTextColor={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons
                name="close-circle"
                size={20}
                color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
              />
            </TouchableOpacity>
          )}
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {filteredFAQs.length > 0 ? (
          filteredFAQs.map(item => (
            <FAQItem
              key={item.id}
              item={item}
              isExpanded={expandedId === item.id}
              onToggle={() => toggleExpand(item.id)}
            />
          ))
        ) : (
          <View style={styles.noResultsContainer}>
            <Ionicons
              name="search-outline"
              size={48}
              color={isDark ? COLORS.text.dark.tertiary : COLORS.text.light.tertiary}
            />
            <Text style={[
              styles.noResultsText,
              { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
            ]}>
              No FAQs found matching your search.
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    padding: SPACING.lg,
    paddingBottom: SPACING.md,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
  },
  searchIcon: {
    marginRight: SPACING.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: SPACING.lg,
    paddingTop: 0,
    paddingBottom: SPACING.xxl,
  },
  faqItem: {
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    ...SHADOWS.light.small,
  },
  faqHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  faqQuestion: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.bold,
    flex: 1,
    marginRight: SPACING.sm,
  },
  faqAnswer: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    marginTop: SPACING.md,
    lineHeight: 22,
  },
  categoryContainer: {
    marginTop: SPACING.md,
  },
  categoryText: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.medium,
  },
  noResultsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  noResultsText: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.medium,
    textAlign: 'center',
    marginTop: SPACING.md,
  },
});
