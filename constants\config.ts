// App configuration constants
import Constants from 'expo-constants';

// Get the API URL from environment variables or use a default
export const API_URL = Constants.expoConfig?.extra?.apiUrl || 'https://inerca-backend.fly.dev';

// Other configuration constants
export const APP_NAME = 'Inerca';
export const APP_VERSION = '1.0.0';

// API endpoints
export const ENDPOINTS = {
  LOGIN: '/api/v1/login',
  REGISTER: '/api/v1/register',
  USER_PROFILE: '/api/v1/user/me',
  RESET_PASSWORD: '/api/v1/reset-password',
};

// Validate required environment variables
const validateConfig = () => {
  if (!API_URL) {
    console.warn('API_URL not configured, using default');
  }
};

validateConfig();

