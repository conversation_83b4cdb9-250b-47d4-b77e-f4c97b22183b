import { useState } from 'react';

/**
 * Generic type for form errors
 */
export type FormErrors<T> = {
  [K in keyof T]?: string;
};

/**
 * Custom hook for form validation
 * @param initialValues Initial form values
 * @returns Form validation utilities
 */
export const useFormValidation = <T extends Record<string, any>>(initialValues: T) => {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<FormErrors<T>>({});
  const [touched, setTouched] = useState<Record<keyof T, boolean>>({} as Record<keyof T, boolean>);

  /**
   * Update a form field value
   * @param field Field name
   * @param value New value
   */
  const handleChange = (field: keyof T, value: any) => {
    setValues((prev) => ({ ...prev, [field]: value }));
    // Clear error when field is changed
    if (errors[field]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  /**
   * Mark a field as touched (user has interacted with it)
   * @param field Field name
   */
  const handleBlur = (field: keyof T) => {
    setTouched((prev) => ({ ...prev, [field]: true }));
  };

  /**
   * Reset form to initial values
   */
  const resetForm = () => {
    setValues(initialValues);
    setErrors({});
    setTouched({} as Record<keyof T, boolean>);
  };

  /**
   * Validate email format
   * @param email Email to validate
   * @returns True if valid
   */
  const isValidEmail = (email: string): boolean => {
    return /\S+@\S+\.\S+/.test(email);
  };

  /**
   * Validate required field
   * @param value Field value
   * @param fieldName Field name for error message
   * @returns Error message or undefined
   */
  const validateRequired = (value: any, fieldName: string): string | undefined => {
    if (!value || (typeof value === 'string' && !value.trim())) {
      return `${fieldName} is required`;
    }
    return undefined;
  };

  /**
   * Validate email field
   * @param email Email to validate
   * @returns Error message or undefined
   */
  const validateEmail = (email: string): string | undefined => {
    if (!email.trim()) {
      return 'Email is required';
    }
    if (!isValidEmail(email)) {
      return 'Email is invalid';
    }
    return undefined;
  };

  /**
   * Validate password field
   * @param password Password to validate
   * @param minLength Minimum length (default: 6)
   * @returns Error message or undefined
   */
  const validatePassword = (password: string, minLength = 6): string | undefined => {
    if (!password) {
      return 'Password is required';
    }
    if (password.length < minLength) {
      return `Password must be at least ${minLength} characters`;
    }
    return undefined;
  };

  /**
   * Validate password confirmation
   * @param password Password
   * @param confirmPassword Confirmation password
   * @returns Error message or undefined
   */
  const validatePasswordMatch = (password: string, confirmPassword: string): string | undefined => {
    if (!confirmPassword) {
      return 'Please confirm your password';
    }
    if (password !== confirmPassword) {
      return 'Passwords do not match';
    }
    return undefined;
  };

  /**
   * Validate phone number
   * @param phoneNumber Phone number to validate
   * @returns Error message or undefined
   */
  const validatePhoneNumber = (phoneNumber: string): string | undefined => {
    if (!phoneNumber.trim()) {
      return 'Phone number is required';
    }
    // Basic phone validation - can be enhanced for specific formats
    if (!/^\d{10,15}$/.test(phoneNumber.replace(/[^0-9]/g, ''))) {
      return 'Please enter a valid phone number';
    }
    return undefined;
  };

  return {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    setErrors,
    resetForm,
    validators: {
      validateRequired,
      validateEmail,
      validatePassword,
      validatePasswordMatch,
      validatePhoneNumber,
    },
  };
};
