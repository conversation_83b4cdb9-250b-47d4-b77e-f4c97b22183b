import React from 'react';
import { SafeAreaView, StyleSheet, useColorScheme } from 'react-native';
import Header from '../../components/ui/Header';
import UnderConstruction from '../../components/UnderConstruction';
import { COLORS } from '../../constants/theme';

export default function Quote() {
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
    ]}>
      <Header
        title="Get Quote"
        showBackButton={false}
        showGreeting={false}
      />

      <UnderConstruction
        title="Get Quote"
        message="The quote feature is coming soon. You'll be able to request insurance quotes directly from the app."
        showBackButton={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
