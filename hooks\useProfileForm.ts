import { useState } from 'react';
import { Alert } from 'react-native';
import { router } from 'expo-router';
import { User } from '../context/AuthContext';

export type ProfileFormErrors = {
  fullName?: string;
  email?: string;
  phoneNumber?: string;
  dateOfBirth?: string;
  address?: string;
  occupation?: string;
  businessName?: string;
  businessLocation?: string;
};

export type ProfileFormData = {
  fullName: string;
  email: string;
  phoneNumber: string;
  dateOfBirth: string;
  address: string;
  occupation: string;
  businessName: string;
  businessLocation: string;
};

export const useProfileForm = (user: User | null, id: string | string[]) => {
  // Form state
  const [formData, setFormData] = useState<ProfileFormData>({
    fullName: user?.username || '',
    email: user?.email || '',
    phoneNumber: user?.phone_number || '',
    dateOfBirth: user?.date_of_birth || '',
    address: user?.user_type === 'business'
      ? user?.business_address || ''
      : user?.physical_address || '',
    occupation: user?.occupation || '',
    businessName: user?.business_name || '',
    businessLocation: user?.business_location || '',
  });

  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<ProfileFormErrors>({});

  // Update form field
  const updateField = (field: keyof ProfileFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Validate form
  const validateForm = () => {
    const newErrors: ProfileFormErrors = {};

    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Full name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = 'Phone number is required';
    }

    if (user?.user_type === 'individual') {
      if (!formData.dateOfBirth) {
        newErrors.dateOfBirth = 'Date of birth is required';
      }

      if (!formData.address.trim()) {
        newErrors.address = 'Address is required';
      }
    }

    if (user?.user_type === 'business') {
      if (!formData.businessName.trim()) {
        newErrors.businessName = 'Business name is required';
      }

      if (!formData.businessLocation.trim()) {
        newErrors.businessLocation = 'Business location is required';
      }

      if (!formData.address.trim()) {
        newErrors.address = 'Business address is required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle save
  const handleSave = () => {
    if (!validateForm()) return;

    setIsLoading(true);

    // Log the profile update
    console.log('Profile update:', {
      id,
      ...formData,
      userType: user?.user_type,
    });

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      Alert.alert(
        'Profile Updated',
        'Your profile has been updated successfully.',
        [
          {
            text: 'OK',
            onPress: () => router.back()
          }
        ]
      );
    }, 1000);
  };

  return {
    formData,
    updateField,
    errors,
    isLoading,
    handleSave,
  };
};
