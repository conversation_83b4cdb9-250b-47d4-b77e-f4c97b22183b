import { Image } from 'expo-image';
import { router } from 'expo-router';
import React, { useRef, useState } from 'react';
import {
  Animated,
  FlatList,
  StyleSheet,
  Text,
  useColorScheme,
  useWindowDimensions,
  View,
} from 'react-native';
import Button from '../../components/ui/Button';
import { BORDER_RADIUS, COLORS, FONTS, SPACING } from '../../constants/theme';

// Define the welcome slides
const slides = [
  {
    id: '1',
    title: 'Welcome to Inerca',
    description: 'Your refined insurance solutions partner',
    image: require('../../assets/images/logo.png'),
  },
  {
    id: '2',
    title: 'Risk Management',
    description: 'Comprehensive business and personal insurance solutions',
    image: require('../../assets/images/logo.png'),
  },
  {
    id: '3',
    title: 'Insurance Made Easy',
    description: 'Manage your policies and claims all in one place',
    image: require('../../assets/images/logo.png'),
  },
];

export default function Welcome() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollX = useRef(new Animated.Value(0)).current;
  const slidesRef = useRef<FlatList>(null);
  const { width } = useWindowDimensions();
  const colorScheme = useColorScheme() || 'light';
  const isDark = colorScheme === 'dark';

  const viewableItemsChanged = useRef(({ viewableItems }: any) => {
    setCurrentIndex(viewableItems[0]?.index || 0);
  }).current;

  const viewConfig = useRef({ viewAreaCoveragePercentThreshold: 50 }).current;

  const scrollTo = (index: number) => {
    if (slidesRef.current) {
      slidesRef.current.scrollToIndex({ index });
    }
  };

  const nextSlide = () => {
    if (currentIndex < slides.length - 1) {
      scrollTo(currentIndex + 1);
    } else {
      // Last slide, navigate to login
      router.replace('/(auth)/register');
    }
  };

  const skipToLogin = () => {
    router.replace('/(auth)/login');
  };

  return (
    <View style={[
      styles.container,
      { backgroundColor: isDark ? COLORS.background.dark : COLORS.background.light }
    ]}>
      <View style={styles.skipContainer}>
        <Button
          title="Skip"
          variant="text"
          onPress={skipToLogin}
          style={styles.skipButton}
        />
      </View>

      <FlatList
        data={slides}
        renderItem={({ item }) => (
          <View style={[styles.slide, { width }]}>
            <Image
              source={item.image}
              style={styles.image}
              contentFit="contain"
            />
            <Text style={[
              styles.title,
              { color: isDark ? COLORS.text.dark.primary : COLORS.text.light.primary }
            ]}>
              {item.title}
            </Text>
            <Text style={[
              styles.description,
              { color: isDark ? COLORS.text.dark.secondary : COLORS.text.light.secondary }
            ]}>
              {item.description}
            </Text>
          </View>
        )}
        horizontal
        showsHorizontalScrollIndicator={false}
        pagingEnabled
        bounces={false}
        keyExtractor={(item) => item.id}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { x: scrollX } } }],
          { useNativeDriver: false }
        )}
        onViewableItemsChanged={viewableItemsChanged}
        viewabilityConfig={viewConfig}
        ref={slidesRef}
      />

      <View style={styles.pagination}>
        {slides.map((_, index) => {
          const isActive = index === currentIndex;
          
          return (
            <View
              key={index}
              style={[
                styles.dot,
                {
                  width: isActive ? 20 : 10,
                  opacity: isActive ? 1 : 0.3,
                  backgroundColor: COLORS.primary,
                },
              ]}
            />
          );
        })}
      </View>

      <View style={styles.buttonContainer}>
        <Button
          title={currentIndex === slides.length - 1 ? "Get Started" : "Next"}
          onPress={nextSlide}
          size="large"
          style={styles.button}
        />

        {currentIndex === slides.length - 1 && (
          <Button
            title="I already have an account"
            variant="text"
            onPress={skipToLogin}
            style={styles.loginButton}
          />
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  skipContainer: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 1,
  },
  skipButton: {
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.sm,
  },
  slide: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: SPACING.xl,
  },
  image: {
    width: 200,
    height: 200,
    marginBottom: SPACING.xl,
  },
  title: {
    fontSize: FONTS.sizes.xxl,
    fontFamily: FONTS.bold,
    textAlign: 'center',
    marginBottom: SPACING.md,
  },
  description: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.regular,
    textAlign: 'center',
    paddingHorizontal: SPACING.lg,
  },
  pagination: {
    flexDirection: 'row',
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dot: {
    height: 10,
    borderRadius: BORDER_RADIUS.round,
    marginHorizontal: 8,
  },
  buttonContainer: {
    width: '100%',
    paddingHorizontal: SPACING.xl,
    marginBottom: SPACING.xxl,
  },
  button: {
    width: '100%',
    marginBottom: SPACING.md,
  },
  loginButton: {
    marginTop: SPACING.sm,
  },
});








